/**
 * @fileoverview Event handlers module for the CHI Dentist Sync application.
 * 
 * This module contains handlers for various events and user interactions, including:
 * - Spreadsheet edit event handlers
 * - Form submission handlers
 * - UI interaction handlers
 * - Scheduled task completion handlers
 * - Error and exception handlers
 * - Webhook and callback handlers
 * 
 * The handlers module provides the connection between user actions or
 * system events and the application's business logic, ensuring appropriate
 * responses to different types of events while maintaining separation of
 * concerns.
 */

/**
 * Handles the onEdit event for the provider's source spreadsheet.
 * Adds a UUID to the corresponding row (in UUID_COL_INDEX) if a relevant production
 * column (e.g., HUMBLE_PROD_COL_INDEX, BAYTOWN_PROD_COL_INDEX) is edited with a valid number,
 * the date (in DATE_COL_INDEX) is valid and not in the future, and no UUID currently exists.
 * Notifies via email on error.
 *
 * @param {GoogleAppsScript.Events.SheetsOnEdit} e The event object.
 */
function onEditHandler(e) {
  let sheetName = 'Unknown'; // Default value
  let editedRow = 0; // Initialize as number
  try {
    // Validate the event object and range
    if (!e || !e.range) return; // Exit if event is invalid

    const sheet = e.range.getSheet();
    sheetName = sheet.getName();
    const editedCol = e.range.getColumn();
    editedRow = e.range.getRow();

    // Check if the edit is in a valid monthly sheet, a production column, and data row
    if (!isMonthlySheet_(sheetName)) return;
    if (editedCol !== HUMBLE_PROD_COL_INDEX && editedCol !== BAYTOWN_PROD_COL_INDEX) return;
    if (editedRow < SOURCE_DATA_START_ROW) return;

    // Check if UUID cell is already filled
    const uuidCell = sheet.getRange(editedRow, UUID_COL_INDEX);
    if (uuidCell.getValue()) return; // Don't overwrite existing UUID

    // --- Check Date Validity and Production Data ---
    const dateCell = sheet.getRange(editedRow, DATE_COL_INDEX);
    const dateValue = dateCell.getValue();
    const humbleProdValue = sheet.getRange(editedRow, HUMBLE_PROD_COL_INDEX).getValue();
    const baytownProdValue = sheet.getRange(editedRow, BAYTOWN_PROD_COL_INDEX).getValue();

    // 1. Validate Date
    if (!dateValue || !(dateValue instanceof Date) || Number.isNaN(dateValue.getTime())) {
      // Logger.log(`onEditHandler: Skipping UUID for row ${editedRow} in "${sheetName}": Invalid/blank date.`);
      return;
    }

    // 2. Check if Date is in the future
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today
    const sourceDate = new Date(dateValue);
    sourceDate.setHours(0, 0, 0, 0); // Normalize source date
    if (sourceDate > today) {
      // Logger.log(`onEditHandler: Skipping UUID for row ${editedRow} in "${sheetName}": Date is in the future.`);
    return;
  }
  
    // 3. Check if *at least one* production value is a valid number
    const hasValidProduction = (typeof humbleProdValue === 'number' && !Number.isNaN(humbleProdValue)) ||
                               (typeof baytownProdValue === 'number' && !Number.isNaN(baytownProdValue));

    if (!hasValidProduction) {
        // Logger.log(`onEditHandler: Skipping UUID for row ${editedRow} in "${sheetName}": No valid production data.`);
        return;
    }
    // --- End Checks ---


    // If all checks pass, add the UUID
    const newUuid = Utilities.getUuid();
    uuidCell.setValue(newUuid);
    // Logger.log(`onEditHandler: Added UUID ${newUuid} to ${sheetName}, row ${editedRow}`);

  } catch (err) {
    const errorMessage = `Error in onEditHandler for sheet "${sheetName}", row ${editedRow}: ${err.message}`;
    Logger.log(`${errorMessage}\n${err.stack}`);
    // Notify on errors during onEdit. Be mindful of potential email volume if errors are frequent.
    notifyError_('onEditHandler', err, `Sheet: ${sheetName}, Row: ${editedRow}`);
  }
}

/**
 * Checks if a sheet name matches the expected monthly format (e.g., "Apr-25", "May 2024").
 * Uses a standard regex (3-letter month, hyphen, 2-digit year), adjust if needed.
 *
 * @param {string} name The sheet name to test.
 * @returns {boolean} True if the name matches the pattern, false otherwise.
 */
function isMonthlySheet_(name) {
  if (!name || typeof name !== 'string') return false;
  // Regex: 3 letters (case-insensitive), hyphen, 2 digits. Anchored.
  const monthlySheetRegex = /^[A-Za-z]{3}-\d{2}$/;
  // Alternative allowing space and 4-digit year: /^[A-Za-z]{3,}[-\s]?\d{2,4}$/i;
  return monthlySheetRegex.test(name.trim());
}

/**
 * Sends an email notification about an error encountered during script execution.
 * Includes script ID and links to execution logs/editor. Attempts to use active user's
 * email, falls back to a hardcoded address if necessary.
 *
 * @param {string} scope A description of where the error occurred (e.g., function name).
 * @param {Error|string} error The error object caught, or an error message string.
 * @param {string} [additionalContext=''] Optional additional context for the email body.
 */
function notifyError_(scope, error, additionalContext = '') {
  try {
    let recipient = '';
    try {
        recipient = Session.getActiveUser().getEmail();
        // Also check if the email looks valid
        if (!recipient || !recipient.includes('@')) {
            Logger.log(`Active user email "${recipient}" seems invalid.`);
            recipient = ''; // Reset if invalid
        }
    } catch (e) {
        Logger.log(`Could not get active user email: ${e.message}. Using fallback.`);
        recipient = ''; // Ensure recipient is empty if session call fails
    }

    // Fallback Email - IMPORTANT: SET THIS
    const FALLBACK_EMAIL = '<EMAIL>'; // <<< REPLACE WITH YOUR MONITORING EMAIL

    if (!recipient) {
       recipient = FALLBACK_EMAIL;
       Logger.log(`Sending notification to fallback: ${recipient}`);
    }

     if (!recipient || !recipient.includes('@')) {
        Logger.log(`ERROR: Invalid recipient email configured ("${recipient}") for error notifications. Cannot send.`);
        return;
     }

    const subject = `[Apps Script Error] ${PROVIDER_NAME} Sync (${scope})`;

    const errorMessage = (error instanceof Error) ? error.message : String(error);
    const errorStack = (error instanceof Error && error.stack) ? error.stack : '(No stack trace available)';

    let body = `An error occurred in the ${PROVIDER_NAME} data sync script.\n\n`;
    body += `Timestamp: ${new Date().toISOString()}\n`;
    body += `Scope/Function: ${scope}\n`;
    if (additionalContext) {
      body += `Context: ${additionalContext}\n`;
    }
    body += `Error Message: ${errorMessage}\n\n`;
    body += `Stack Trace:\n${errorStack}\n`;

    const scriptId = ScriptApp.getScriptId();
    body += '---\n';
    body += `Script ID: ${scriptId}\n`;
    body += `Source Sheet: https://docs.google.com/spreadsheets/d/${SOURCE_SHEET_ID}/edit\n`;
    body += `Master Sheet: https://docs.google.com/spreadsheets/d/${MASTER_SPREADSHEET_ID}/edit\n`;
    body += `View Executions: https://script.google.com/home/<USER>/${scriptId}/executions\n`;
    body += `Open Script Editor: https://script.google.com/d/${scriptId}/edit\n`;

    Logger.log(`Sending error notification to ${recipient} for scope "${scope}"`);
    MailApp.sendEmail({
      to: recipient,
      subject: subject,
      body: body,
    });

  } catch (mailError) {
    Logger.log(`CRITICAL ERROR: Failed to send error notification email. Scope: ${scope}. Original Error: ${error.message || error}. Mail Error: ${mailError.message}\n${mailError.stack}`);
  }
}