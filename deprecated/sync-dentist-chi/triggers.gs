/**
 * @fileoverview Trigger management module for the CHI Dentist Sync application.
 * 
 * This module handles all aspects of Google Apps Script triggers, including:
 * - Creation and deletion of time-based triggers
 * - Management of event-based triggers (onEdit, onOpen, etc.)
 * - Scheduling of recurring sync operations
 * - Trigger error handling and recovery
 * - Coordination of multiple triggers to prevent conflicts
 * - Trigger status monitoring and reporting
 * 
 * The triggers module ensures that automated operations run reliably
 * on schedule, providing the foundation for automated synchronization
 * without manual intervention.
 */

/**
 * Deletes existing time-driven triggers for a specified handler function and creates a new one.
 *
 * @param {string} functionName The name of the function to trigger (e.g., 'syncToExternalMaster').
 * @param {number} hours The frequency in hours. Throws error if invalid.
 */
function reinstallTrigger_(functionName, hours) {
  // Check if the function exists in the global scope (tricky in Apps Script, basic check)
  if (typeof this[functionName] !== 'function') {
      throw new Error(`Trigger installation failed: Function "${functionName}" does not seem to exist or is not global.`);
  }
  if (typeof hours !== 'number' || hours <= 0 || !Number.isInteger(hours * 60)) {
     throw new Error(`Trigger installation failed: Invalid hours value "${hours}". Must be a positive number resulting in whole minutes.`);
  }

  deleteTriggersByHandler_(functionName); // Delete existing triggers first
  ScriptApp.newTrigger(functionName)
    .timeBased()
    .everyHours(hours)
    .create();
  Logger.log(`Installed time-driven trigger for "${functionName}" to run every ${hours} hours.`);
}

/**
 * Deletes existing edit triggers for the defined EDIT_HANDLER_FUNCTION_NAME
 * and creates a new onEdit trigger bound to the SOURCE_SHEET_ID.
 */
function reinstallEditTrigger_() {
   // Use the constant defined in the configuration section
   const functionName = EDIT_HANDLER_FUNCTION_NAME;
    if (typeof this[functionName] !== 'function') {
      throw new Error(`Trigger installation failed: Function "${functionName}" does not seem to exist or is not global.`);
   }
   // Delete existing triggers for this handler first
   deleteTriggersByHandler_(functionName); // Use constant

   // Create a new specific trigger for the source spreadsheet
   ScriptApp.newTrigger(functionName) // Use constant
     .forSpreadsheet(SOURCE_SHEET_ID) // Bind to THIS provider's sheet
     .onEdit()
     .create();
   Logger.log(`Installed onEdit trigger for "${functionName}" on Spreadsheet ID ${SOURCE_SHEET_ID}.`);
}

/**
 * Deletes all triggers associated with a specific handler function name for the current script project.
 *
 * @param {string} functionName The handler function name (e.g., 'syncToExternalMaster', 'onEditHandler').
 */
function deleteTriggersByHandler_(functionName) {
  let deletedCount = 0;
  try {
      const triggers = ScriptApp.getProjectTriggers();
      for (const trigger of triggers) {
        if (trigger.getHandlerFunction() === functionName) {
          try {
             ScriptApp.deleteTrigger(trigger);
             deletedCount++;
          } catch (e) {
             Logger.log(`Warning: Failed to delete trigger for "${functionName}" (ID: ${trigger.getUniqueId()}): ${e.message}`);
          }
        }
      }
  } catch (e) {
      Logger.log(`Error accessing project triggers for deletion: ${e.message}. Manual check might be needed.`);
      // Optionally notify here if access fails
      return;
  }

  if (deletedCount > 0) {
     Logger.log(`Deleted ${deletedCount} existing trigger(s) for handler function "${functionName}".`);
  } else {
     // This is normal if run for the first time or after manual deletion
     // Logger.log(`No existing triggers found for handler function "${functionName}".`);
  }
}

/**
 * Lists all triggers currently associated with this script project.
 * Useful for debugging trigger issues.
 */
function listAllTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  if (triggers.length === 0) {
      Logger.log('No triggers found for this project.');
      return;
  }
  Logger.log(`Found ${triggers.length} trigger(s):`);
  for (const t of triggers) {
    Logger.log(
      `- Handler: ${t.getHandlerFunction()}, Type: ${t.getEventType()}, Source: ${t.getTriggerSource()}, ID: ${t.getUniqueId()}`
    );
  }
}