/**
 * @fileoverview Data mapping module for the CHI Dentist Sync application.
 * 
 * This module handles all data transformation and field mapping, including:
 * - Translation between external API formats and internal data structures
 * - Field normalization and standardization
 * - Data type conversion and validation
 * - Complex transformations and calculations
 * - Schema versioning and migration support
 * 
 * The mapping module ensures that data is correctly transformed between
 * different systems and formats, maintaining data integrity throughout
 * the synchronization process while accommodating differences in data
 * models between systems.
 */

/**
 * Builds a single row array formatted for the master sheet based on master headers.
 *
 * @param {string[]} masterHeaders An array of header names from the master sheet.
 * @param {any[]} sourceRowData An array representing a single row from the source sheet.
 * @param {string} sourceSheetName The name of the source sheet this row came from.
 * @returns {any[]} An array formatted for the master sheet row.
 * @throws {Error} If essential source data (like UUID) is missing unexpectedly.
 */
function buildMasterRow_(masterHeaders, sourceRowData, sourceSheetName) {
  const masterRowOutput = Array(masterHeaders.length).fill(''); // Initialize empty row

  // Mapping based on known structure and constants for THIS provider
  const uuid = sourceRowData[UUID_COL_INDEX - 1]; // 0-based
  if (!uuid) {
      throw new Error(`Attempted to build master row with missing UUID from sheet ${sourceSheetName}.`);
  }

  // Direct mapping using assignValue_ helper
  assignValue_(masterRowOutput, masterHeaders, 'date',                   sourceRowData[DATE_COL_INDEX - 1]); // Col A
  // 'hours_worked' - Not available in Obi's sheet, leave blank
  // 'location' - Not applicable/fixed for Obi, leave blank
  assignValue_(masterRowOutput, masterHeaders, 'provider_name',          PROVIDER_NAME);    // Constant
  assignValue_(masterRowOutput, masterHeaders, 'provider_type',          PROVIDER_TYPE);    // Constant
  assignValue_(masterRowOutput, masterHeaders, 'source_sheet',           sourceSheetName);  // Variable
  // 'production_goal_daily' - Not available in Obi's sheet, leave blank
  // 'verified_production' - Not applicable/separated in Obi's sheet, leave blank
  // 'bonus' - Not available in Obi's sheet, leave blank
  assignValue_(masterRowOutput, masterHeaders, 'humble_production',    sourceRowData[HUMBLE_PROD_COL_INDEX - 1]); // Col C
  assignValue_(masterRowOutput, masterHeaders, 'baytown_production',   sourceRowData[BAYTOWN_PROD_COL_INDEX - 1]);// Col D
  assignValue_(masterRowOutput, masterHeaders, 'monthly_goal',         sourceRowData[MONTHLY_GOAL_COL_INDEX - 1]); // Col J
  // 'external_id' - Not applicable for Obi, leave blank
  assignValue_(masterRowOutput, masterHeaders, 'uuid',                   uuid); // Col O (or as per UUID_COL_INDEX)


  // --- Data Type Handling ---
  // Ensure numbers are numbers, dates are dates, etc. before final output.
  // The assignValue_ handles null/undefined to '', but let's specifically ensure numbers.
  const humbleProdIndex = masterHeaders.findIndex(h => h.toLowerCase() === 'humble_production');
  if (humbleProdIndex !== -1 && typeof masterRowOutput[humbleProdIndex] !== 'number') {
      masterRowOutput[humbleProdIndex] = masterRowOutput[humbleProdIndex] ? Number.parseFloat(masterRowOutput[humbleProdIndex]) || 0 : 0;
  }
  const baytownProdIndex = masterHeaders.findIndex(h => h.toLowerCase() === 'baytown_production');
  if (baytownProdIndex !== -1 && typeof masterRowOutput[baytownProdIndex] !== 'number') {
      masterRowOutput[baytownProdIndex] = masterRowOutput[baytownProdIndex] ? Number.parseFloat(masterRowOutput[baytownProdIndex]) || 0 : 0;
  }
  const monthlyGoalIndex = masterHeaders.findIndex(h => h.toLowerCase() === 'monthly_goal');
   if (monthlyGoalIndex !== -1 && typeof masterRowOutput[monthlyGoalIndex] !== 'number') {
      masterRowOutput[monthlyGoalIndex] = masterRowOutput[monthlyGoalIndex] ? Number.parseFloat(masterRowOutput[monthlyGoalIndex]) || 0 : 0;
  }
  // Ensure Date is a Date object (should be if input was valid Date)
  const dateIndex = masterHeaders.findIndex(h => h.toLowerCase() === 'date');
  if (dateIndex !== -1 && !(masterRowOutput[dateIndex] instanceof Date) && masterRowOutput[dateIndex]) {
      // Attempt to parse if it's not already a Date (less likely path if validation worked)
      const parsedDate = new Date(masterRowOutput[dateIndex]);
      if (!Number.isNaN(parsedDate.getTime())) {
          masterRowOutput[dateIndex] = parsedDate;
      } else {
          masterRowOutput[dateIndex] = ''; // Fallback to blank if parsing fails
          Logger.log(`Warning: Could not parse date "${sourceRowData[DATE_COL_INDEX - 1]}" for UUID ${uuid} during build.`);
      }
  } else if (dateIndex !== -1 && !masterRowOutput[dateIndex]){
     masterRowOutput[dateIndex] = ''; // Ensure blank if original was blank/invalid
  }


  return masterRowOutput;
}

/**
 * Safely assigns a value to the correct position in the output row array based on the header name.
 * Finds the index of the header name (case-insensitive) and places the value there.
 * Converts null or undefined values to empty strings.
 * If the header name is not found, it logs a warning but does not throw an error.
 *
 * @param {any[]} outputRow The array representing the row being built.
 * @param {string[]} headers The array of header names from the master sheet.
 * @param {string} headerName The name of the header column to assign the value to.
 * @param {*} value The value to assign.
 */
function assignValue_(outputRow, headers, headerName, value) {
  const lowerCaseHeaderName = headerName.toLowerCase();
  const index = headers.findIndex(h => h.toString().toLowerCase() === lowerCaseHeaderName);
  if (index !== -1) {
    // Store null/undefined as blanks, otherwise use the value
    outputRow[index] = (value === null || value === undefined) ? '' : value;
  } else {
    Logger.log(`Warning: Header "${headerName}" not found in master sheet headers during row construction.`);
  }
}