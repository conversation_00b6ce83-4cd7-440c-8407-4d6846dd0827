/**
 * @fileoverview Utility functions module for the CHI Dentist Sync application.
 * 
 * This module provides general-purpose utility functions used throughout
 * the application, including:
 * - Date and time manipulation
 * - String formatting and parsing
 * - Array and object manipulation
 * - Spreadsheet helper functions
 * - Caching and memoization utilities
 * - Rate limiting and throttling mechanisms
 * - Common validation routines
 * 
 * The utils module centralizes commonly used functions to promote code
 * reuse, consistency, and maintainability across the application.
 */

/**
 * Safely assigns a value to the correct position in the output row array based on the header name.
 * Finds the index of the header name (case-insensitive) and places the value there.
 * Converts null or undefined values to empty strings.
 * If the header name is not found, it logs a warning but does not throw an error.
 *
 * @param {any[]} outputRow The array representing the row being built.
 * @param {string[]} headers The array of header names from the master sheet.
 * @param {string} headerName The name of the header column to assign the value to.
 * @param {*} value The value to assign.
 */
function assignValue_(outputRow, headers, headerName, value) {
  const lowerCaseHeaderName = headerName.toLowerCase();
  const index = headers.findIndex(h => h.toString().toLowerCase() === lowerCaseHeaderName);
  if (index !== -1) {
    // Store null/undefined as blanks, otherwise use the value
    outputRow[index] = (value === null || value === undefined) ? '' : value;
  } else {
    Logger.log(`Warning: Header "${headerName}" not found in master sheet headers during row construction.`);
  }
}

/**
 * Checks if a sheet name matches the expected monthly format (e.g., "Apr-25", "May 2024").
 * Uses a standard regex (3-letter month, hyphen, 2-digit year), adjust if needed.
 *
 * @param {string} name The sheet name to test.
 * @returns {boolean} True if the name matches the pattern, false otherwise.
 */
function isMonthlySheet_(name) {
  if (!name || typeof name !== 'string') return false;
  // Regex: 3 letters (case-insensitive), hyphen, 2 digits. Anchored.
  const monthlySheetRegex = /^[A-Za-z]{3}-\d{2}$/;
  // Alternative allowing space and 4-digit year: /^[A-Za-z]{3,}[-\s]?\d{2,4}$/i;
  return monthlySheetRegex.test(name.trim());
}

/**
 * Seeds UUID values (in UUID_COL_INDEX) for all rows in monthly sheets
 * that are missing them but have valid data (Date, Production).
 * Skips rows with future dates or no valid production data.
 * Typically run once during initial setup.
 */
function seedAllMissingUuids_() {
  Logger.log(`Starting UUID seeding for ${PROVIDER_NAME} monthly sheets...`);
  const ss = SpreadsheetApp.openById(SOURCE_SHEET_ID);
  if (!ss) {
    throw new Error(`Cannot seed UUIDs: Source Spreadsheet (${SOURCE_SHEET_ID}) not found or inaccessible.`);
  }
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Normalize

  let totalSeeded = 0;
  const sheets = ss.getSheets();

  for (const sh of sheets) {
    const sheetName = sh.getName();
    if (!isMonthlySheet_(sheetName)) continue;

    Logger.log(`Seeding UUIDs in sheet: "${sheetName}"`);
    const lastRow = sh.getLastRow();
    if (lastRow < SOURCE_DATA_START_ROW) {
      Logger.log(`Skipping "${sheetName}" - no data rows found.`);
      continue;
    }

    // Determine the max column index needed for reading checks + UUID
    const maxColIndex = Math.max(UUID_COL_INDEX, DATE_COL_INDEX, HUMBLE_PROD_COL_INDEX, BAYTOWN_PROD_COL_INDEX);
    const numRows = lastRow - SOURCE_DATA_START_ROW + 1;
    if (numRows <= 0) continue;

    // --- Read relevant columns (Date, Humble Prod, Baytown Prod, UUID) ---
    const dataRange = sh.getRange(SOURCE_DATA_START_ROW, 1, numRows, maxColIndex);
    const values = dataRange.getValues();
    let sheetSeeded = 0;
    let changesMade = false; // Flag to optimize writing

    for (let i = 0; i < values.length; i++) {
      const currentRow = values[i];
      const uuid = currentRow[UUID_COL_INDEX - 1]; // 0-based

      // Only proceed if UUID is missing
      if (!uuid) {
          const dateValue = currentRow[DATE_COL_INDEX - 1];
          const humbleProd = currentRow[HUMBLE_PROD_COL_INDEX - 1];
          const baytownProd = currentRow[BAYTOWN_PROD_COL_INDEX - 1];

          // --- Perform Validation Checks ---
          // 1. Check Date validity
          if (!dateValue || !(dateValue instanceof Date) || Number.isNaN(dateValue.getTime())) {
              continue; // Skip if date is invalid/blank
          }
          // 2. Check Date is not in the future
          const sourceDate = new Date(dateValue);
          sourceDate.setHours(0, 0, 0, 0);
          if (sourceDate > today) {
              continue; // Skip if date is in the future
          }
          // 3. Check for *at least one* valid production value
          const hasValidProduction = (typeof humbleProd === 'number' && !Number.isNaN(humbleProd)) ||
                                     (typeof baytownProd === 'number' && !Number.isNaN(baytownProd));
          if (!hasValidProduction) {
             continue; // Skip if no valid production data
          }
          // --- End Checks ---

          // If all checks pass, generate and assign UUID to the array
          currentRow[UUID_COL_INDEX - 1] = Utilities.getUuid();
          sheetSeeded++;
          changesMade = true;
      } // End if(!uuid)
    } // End loop through rows

    // Write back to the sheet ONLY if changes were made
    if (changesMade) {
      // Get the range specifically for the UUID column to write back to
      const uuidColumnRange = sh.getRange(SOURCE_DATA_START_ROW, UUID_COL_INDEX, numRows, 1);
      // Extract just the UUID column from the potentially modified 'values' array
      const uuidValuesToWrite = values.map(row => [row[UUID_COL_INDEX - 1]]);
      uuidColumnRange.setValues(uuidValuesToWrite);

      totalSeeded += sheetSeeded;
      Logger.log(`Added ${sheetSeeded} UUIDs to sheet "${sheetName}"`);
    } else {
      Logger.log(`No missing UUIDs requiring seeding found in sheet "${sheetName}"`);
    }
    SpreadsheetApp.flush(); // Ensure writes are committed before next sheet
  } // End loop through sheets

  Logger.log(`UUID seeding completed for ${PROVIDER_NAME}. Added ${totalSeeded} UUIDs across all valid monthly sheets.`);
}

/**
 * Lists all triggers currently associated with this script project.
 * Useful for debugging trigger issues.
 */
function listAllTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  if (triggers.length === 0) {
      Logger.log('No triggers found for this project.');
      return;
  }
  Logger.log(`Found ${triggers.length} trigger(s):`);
  for (const t of triggers) {
    Logger.log(
      `- Handler: ${t.getHandlerFunction()}, Type: ${t.getEventType()}, Source: ${t.getTriggerSource()}, ID: ${t.getUniqueId()}`
    );
  }
}