/**
 * @fileoverview Notification module for the CHI Dentist Sync application.
 * 
 * This module handles error notifications and alerts, including:
 * - Email notifications for critical errors
 * - In-app alerts and status messages
 * - Integration with external notification systems
 * - Notification throttling to prevent alert fatigue
 * - Customizable notification templates
 * - Notification history and tracking
 * 
 * The notification module ensures that administrators and users are
 * promptly informed of important events and errors, enabling quick
 * response to issues that require human intervention.
 */

/**
 * Sends an email notification about an error encountered during script execution.
 * Includes script ID and links to execution logs/editor. Attempts to use active user's
 * email, falls back to a hardcoded address if necessary.
 *
 * @param {string} scope A description of where the error occurred (e.g., function name).
 * @param {Error|string} error The error object caught, or an error message string.
 * @param {string} [additionalContext=''] Optional additional context for the email body.
 */
function notifyError_(scope, error, additionalContext = '') {
  try {
    let recipient = '';
    try {
        recipient = Session.getActiveUser().getEmail();
        // Also check if the email looks valid
        if (!recipient || !recipient.includes('@')) {
            Logger.log(`Active user email "${recipient}" seems invalid.`);
            recipient = ''; // Reset if invalid
        }
    } catch (e) {
        Logger.log(`Could not get active user email: ${e.message}. Using fallback.`);
        recipient = ''; // Ensure recipient is empty if session call fails
    }

    // Fallback Email - IMPORTANT: SET THIS
    const FALLBACK_EMAIL = '<EMAIL>'; // <<< REPLACE WITH YOUR MONITORING EMAIL

    if (!recipient) {
       recipient = FALLBACK_EMAIL;
       Logger.log(`Sending notification to fallback: ${recipient}`);
    }

     if (!recipient || !recipient.includes('@')) {
        Logger.log(`ERROR: Invalid recipient email configured ("${recipient}") for error notifications. Cannot send.`);
        return;
     }

    const subject = `[Apps Script Error] ${PROVIDER_NAME} Sync (${scope})`;

    const errorMessage = (error instanceof Error) ? error.message : String(error);
    const errorStack = (error instanceof Error && error.stack) ? error.stack : '(No stack trace available)';

    let body = `An error occurred in the ${PROVIDER_NAME} data sync script.\n\n`;
    body += `Timestamp: ${new Date().toISOString()}\n`;
    body += `Scope/Function: ${scope}\n`;
    if (additionalContext) {
      body += `Context: ${additionalContext}\n`;
    }
    body += `Error Message: ${errorMessage}\n\n`;
    body += `Stack Trace:\n${errorStack}\n`;

    const scriptId = ScriptApp.getScriptId();
    body += '---\n';
    body += `Script ID: ${scriptId}\n`;
    body += `Source Sheet: https://docs.google.com/spreadsheets/d/${SOURCE_SHEET_ID}/edit\n`;
    body += `Master Sheet: https://docs.google.com/spreadsheets/d/${MASTER_SPREADSHEET_ID}/edit\n`;
    body += `View Executions: https://script.google.com/home/<USER>/${scriptId}/executions\n`;
    body += `Open Script Editor: https://script.google.com/d/${scriptId}/edit\n`;

    Logger.log(`Sending error notification to ${recipient} for scope "${scope}"`);
    MailApp.sendEmail({
      to: recipient,
      subject: subject,
      body: body,
    });

  } catch (mailError) {
    Logger.log(`CRITICAL ERROR: Failed to send error notification email. Scope: ${scope}. Original Error: ${error.message || error}. Mail Error: ${mailError.message}\n${mailError.stack}`);
  }
}