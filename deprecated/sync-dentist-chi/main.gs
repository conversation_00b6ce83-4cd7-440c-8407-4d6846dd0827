/**
 * @fileoverview Main entry point module for the CHI Dentist Sync application.
 * 
 * This module contains:
 * - Global entry point functions that can be triggered from the Google Sheets UI
 * - Custom menu setup and configuration
 * - High-level orchestration of the sync process
 * - Application initialization and lifecycle management
 * 
 * This serves as the primary interface between the user and the application,
 * providing access to core functionality through the Google Sheets menu system
 * and handling the overall execution flow.
 */

/**
 * Adds a custom menu to the spreadsheet UI.
 */
function onOpen() {
  SpreadsheetApp.getUi()
    .createMenu(`Sync Tools (${PROVIDER_NAME})`)
    .addItem('Run Full Sync Now', SYNC_FUNCTION_NAME)
    .addSeparator()
    .addItem('Run Setup (Triggers, UUIDs, Logs)', SETUP_FUNCTION_NAME)
    .addItem('Seed Missing UUIDs Only', SEED_UUIDS_FUNCTION_NAME)
    .addToUi();
}

/**
 * Lists all triggers currently associated with this script project.
 * Useful for debugging trigger issues.
 */
function listAllTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  if (triggers.length === 0) {
      Logger.log('No triggers found for this project.');
      return;
  }
  Logger.log(`Found ${triggers.length} trigger(s):`);
  for (const t of triggers) {
    Logger.log(
      `- Handler: ${t.getHandlerFunction()}, Type: ${t.getEventType()}, Source: ${t.getTriggerSource()}, ID: ${t.getUniqueId()}`
    );
  }
}