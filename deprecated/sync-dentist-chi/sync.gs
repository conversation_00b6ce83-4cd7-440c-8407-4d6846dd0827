/**
 * @fileoverview Core synchronization module for the CHI Dentist Sync application.
 * 
 * This module contains the primary synchronization logic, including:
 * - Data fetching from external sources (APIs, databases, etc.)
 * - Comparison of local and remote data to identify changes
 * - Conflict detection and resolution strategies
 * - Batch processing and chunking for large datasets
 * - Error handling and recovery during sync operations
 * - Progress tracking and reporting
 * 
 * The sync module is responsible for ensuring data consistency between
 * the Google Sheet and external systems, handling the complexities of
 * bidirectional data synchronization.
 */

/**
 * Synchronizes the provider's data from source monthly sheets to the master sheet.
 * Identifies new rows based on UUIDs and appends them to the master data tab.
 * Logs the execution details to the log tab.
 */
function syncToExternalMaster() {
  const functionName = SYNC_FUNCTION_NAME;
  const runStart = new Date();
  let status = 'Success';
  let errorMessage = '';
  let inspected = 0;
  let skippedNoUuid = 0;
  let skippedDup = 0;
  let skippedInvalid = 0; // Counter for rows skipped due to invalid date/prod
  let added = 0;

  try {
    Logger.log(`Starting sync for ${PROVIDER_NAME}...`);
    Logger.log(`Master URL → ${SpreadsheetApp.openById(MASTER_SPREADSHEET_ID).getUrl()}`);

    // --- Open Spreadsheets ---
    const sourceSpreadsheet = SpreadsheetApp.openById(SOURCE_SHEET_ID);
    if (!sourceSpreadsheet) throw new Error(`Source Spreadsheet not found or inaccessible (ID: ${SOURCE_SHEET_ID})`);

    const masterSpreadsheet = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
    if (!masterSpreadsheet) throw new Error(`Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);

    const masterSheet = masterSpreadsheet.getSheetByName(MASTER_TAB_NAME);
    if (!masterSheet) throw new Error(`Master tab "${MASTER_TAB_NAME}" not found in Master Sheet (ID: ${MASTER_SHEET_ID})`);

    // --- Get Master Sheet Headers and UUID Column Index ---
    const masterLastCol = masterSheet.getLastColumn();
    if (masterLastCol === 0 || masterSheet.getLastRow() === 0) {
       throw new Error(`Master tab "${MASTER_TAB_NAME}" appears to be empty or header row is missing.`);
    }
    const masterHeaders = masterSheet.getRange(1, 1, 1, masterLastCol).getValues()[0];
    const uuidMasterColIndex = masterHeaders.findIndex(h => h.toString().toLowerCase() === 'uuid'); // 0-based index

    if (uuidMasterColIndex === -1) throw new Error('Column "uuid" not found in master header row 1.');

    // --- Build Set of Existing UUIDs from Master Sheet ---
    const masterLastRow = masterSheet.getLastRow();
    const existingUuids = new Set();
    if (masterLastRow > 1) {
      const uuidValues = masterSheet.getRange(2, uuidMasterColIndex + 1, masterLastRow - 1, 1)
                                  .getValues()
                                  .flat()
                                  .filter(String);
      for (const uuid of uuidValues) {
          existingUuids.add(uuid);
      }
    }
    Logger.log(`Found ${existingUuids.size} existing UUIDs in master sheet.`);

    // --- Process Source Sheets ---
    const rowsToAppendBatch = [];
    const sourceSheets = sourceSpreadsheet.getSheets();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (const sheet of sourceSheets) {
      const sheetName = sheet.getName();
      if (!isMonthlySheet_(sheetName)) continue;

      Logger.log(`Processing sheet: "${sheetName}"`);
      const lastRow = sheet.getLastRow();
      if (lastRow < SOURCE_DATA_START_ROW) {
        Logger.log(`Skipping sheet "${sheetName}" - no data found starting from row ${SOURCE_DATA_START_ROW}.`);
        continue;
      }
      
      // Read all relevant data from the current sheet
      const lastColSource = sheet.getLastColumn();
      // Ensure we read up to the highest index needed (UUID_COL_INDEX)
      const readLastCol = Math.max(lastColSource, UUID_COL_INDEX, DATE_COL_INDEX, HUMBLE_PROD_COL_INDEX, BAYTOWN_PROD_COL_INDEX, MONTHLY_GOAL_COL_INDEX);
      const numRowsToRead = lastRow - SOURCE_DATA_START_ROW + 1;
      if (numRowsToRead <= 0) continue; // Should be caught by lastRow check, but safety first

      const sheetData = sheet.getRange(SOURCE_DATA_START_ROW, 1, numRowsToRead, readLastCol).getValues();

      for (const [index, row] of sheetData.entries()) {
        const sourceRowNum = SOURCE_DATA_START_ROW + index;
        inspected++;

        const uuid = row[UUID_COL_INDEX - 1]; // 0-based index
        const dateValue = row[DATE_COL_INDEX - 1];
        const humbleProd = row[HUMBLE_PROD_COL_INDEX - 1];
        const baytownProd = row[BAYTOWN_PROD_COL_INDEX - 1];

        // --- VALIDATION CHECKS ---
        // 1. Check Date validity
        if (!dateValue || !(dateValue instanceof Date) || Number.isNaN(dateValue.getTime())) {
            // Logger.log(`Sync Skipping row ${sourceRowNum} in "${sheetName}": Invalid/blank date.`);
            skippedInvalid++;
            continue;
        }

        // 2. Check Date is not in the future
        const sourceDate = new Date(dateValue);
        sourceDate.setHours(0, 0, 0, 0); // Normalize
        if (sourceDate > today) {
            // Logger.log(`Sync Skipping row ${sourceRowNum} in "${sheetName}": Date is in the future.`);
            skippedInvalid++;
            continue;
        }

        // 3. Check for *at least one* valid production value
        const hasValidProduction = (typeof humbleProd === 'number' && !Number.isNaN(humbleProd)) ||
                                   (typeof baytownProd === 'number' && !Number.isNaN(baytownProd));
        if (!hasValidProduction) {
           // Logger.log(`Sync Skipping row ${sourceRowNum} in "${sheetName}": No valid production data.`);
           skippedInvalid++;
           continue;
        }
        // --- END VALIDATION CHECKS ---


        // --- UUID Checks ---
        if (!uuid) {
          skippedNoUuid++;
          continue;
        }
        if (existingUuids.has(uuid)) {
          skippedDup++;
          continue;
        }
        // --- End UUID Checks ---


        // --- Build Row for Master Sheet ---
        try {
           const masterRow = buildMasterRow_(masterHeaders, row, sheetName);
           rowsToAppendBatch.push(masterRow);
           existingUuids.add(uuid); // Add to set to prevent duplicates within this run
           added++;
        } catch (buildErr) {
           Logger.log(`Error building master row from source sheet "${sheetName}", row ${sourceRowNum}: ${buildErr.message}`);
           // Consider adding to a 'failed rows' count or similar if needed
        }
      } // End inner loop (rows)
    } // End outer loop (sheets)

    // --- Write to Master Sheet ---
    if (rowsToAppendBatch.length > 0) {
      const targetRowNum = masterSheet.getLastRow() + 1;
      const numRowsToAppend = rowsToAppendBatch.length;
      const numColsToAppend = masterHeaders.length;

      if (numColsToAppend > 0) {
        Logger.log(`Attempting to write ${numRowsToAppend} new rows to master sheet.`);
        const targetRange = masterSheet.getRange(targetRowNum, 1, numRowsToAppend, numColsToAppend);
        targetRange.setValues(rowsToAppendBatch);
        Logger.log(`Successfully appended ${numRowsToAppend} rows.`);
      } else {
          throw new Error("Master header length is zero. Cannot determine number of columns to append.");
      }
    } else {
      Logger.log('No new valid rows found to append.');
    }

  } catch (err) {
    Logger.log(`ERROR in ${functionName}: ${err.message}\n${err.stack}`);
    status = 'Failure';
    errorMessage = err.message;
    notifyError_(functionName, err);
  } finally {
    const runEnd = new Date();
    const durationSeconds = (runEnd.getTime() - runStart.getTime()) / 1000;
    // Log with the new skippedInvalid count
    logRun_(runStart, status, inspected, skippedNoUuid, skippedDup, skippedInvalid, added, durationSeconds, errorMessage);
    Logger.log(`${functionName} finished. Status: ${status}. Duration: ${durationSeconds.toFixed(2)}s. Added: ${added}. Skipped (No UUID/Dup/Invalid): ${skippedNoUuid}/${skippedDup}/${skippedInvalid}.`);
  }
}

/**
 * Builds a single row array formatted for the master sheet based on master headers.
 *
 * @param {string[]} masterHeaders An array of header names from the master sheet.
 * @param {any[]} sourceRowData An array representing a single row from the source sheet.
 * @param {string} sourceSheetName The name of the source sheet this row came from.
 * @returns {any[]} An array formatted for the master sheet row.
 * @throws {Error} If essential source data (like UUID) is missing unexpectedly.
 */
function buildMasterRow_(masterHeaders, sourceRowData, sourceSheetName) {
  const masterRowOutput = Array(masterHeaders.length).fill(''); // Initialize empty row

  // Mapping based on known structure and constants for THIS provider
  const uuid = sourceRowData[UUID_COL_INDEX - 1]; // 0-based
  if (!uuid) {
      throw new Error(`Attempted to build master row with missing UUID from sheet ${sourceSheetName}.`);
  }

  // Direct mapping using assignValue_ helper
  assignValue_(masterRowOutput, masterHeaders, 'date',                   sourceRowData[DATE_COL_INDEX - 1]); // Col A
  // 'hours_worked' - Not available in Obi's sheet, leave blank
  // 'location' - Not applicable/fixed for Obi, leave blank
  assignValue_(masterRowOutput, masterHeaders, 'provider_name',          PROVIDER_NAME);    // Constant
  assignValue_(masterRowOutput, masterHeaders, 'provider_type',          PROVIDER_TYPE);    // Constant
  assignValue_(masterRowOutput, masterHeaders, 'source_sheet',           sourceSheetName);  // Variable
  // 'production_goal_daily' - Not available in Obi's sheet, leave blank
  // 'verified_production' - Not applicable/separated in Obi's sheet, leave blank
  // 'bonus' - Not available in Obi's sheet, leave blank
  assignValue_(masterRowOutput, masterHeaders, 'humble_production',    sourceRowData[HUMBLE_PROD_COL_INDEX - 1]); // Col C
  assignValue_(masterRowOutput, masterHeaders, 'baytown_production',   sourceRowData[BAYTOWN_PROD_COL_INDEX - 1]);// Col D
  assignValue_(masterRowOutput, masterHeaders, 'monthly_goal',         sourceRowData[MONTHLY_GOAL_COL_INDEX - 1]); // Col J
  // 'external_id' - Not applicable for Obi, leave blank
  assignValue_(masterRowOutput, masterHeaders, 'uuid',                   uuid); // Col O (or as per UUID_COL_INDEX)


  // --- Data Type Handling ---
  // Ensure numbers are numbers, dates are dates, etc. before final output.
  // The assignValue_ handles null/undefined to '', but let's specifically ensure numbers.
  const humbleProdIndex = masterHeaders.findIndex(h => h.toLowerCase() === 'humble_production');
  if (humbleProdIndex !== -1 && typeof masterRowOutput[humbleProdIndex] !== 'number') {
      masterRowOutput[humbleProdIndex] = masterRowOutput[humbleProdIndex] ? Number.parseFloat(masterRowOutput[humbleProdIndex]) || 0 : 0;
  }
  const baytownProdIndex = masterHeaders.findIndex(h => h.toLowerCase() === 'baytown_production');
  if (baytownProdIndex !== -1 && typeof masterRowOutput[baytownProdIndex] !== 'number') {
      masterRowOutput[baytownProdIndex] = masterRowOutput[baytownProdIndex] ? Number.parseFloat(masterRowOutput[baytownProdIndex]) || 0 : 0;
  }
  const monthlyGoalIndex = masterHeaders.findIndex(h => h.toLowerCase() === 'monthly_goal');
   if (monthlyGoalIndex !== -1 && typeof masterRowOutput[monthlyGoalIndex] !== 'number') {
      masterRowOutput[monthlyGoalIndex] = masterRowOutput[monthlyGoalIndex] ? Number.parseFloat(masterRowOutput[monthlyGoalIndex]) || 0 : 0;
  }
  // Ensure Date is a Date object (should be if input was valid Date)
  const dateIndex = masterHeaders.findIndex(h => h.toLowerCase() === 'date');
  if (dateIndex !== -1 && !(masterRowOutput[dateIndex] instanceof Date) && masterRowOutput[dateIndex]) {
      // Attempt to parse if it's not already a Date (less likely path if validation worked)
      const parsedDate = new Date(masterRowOutput[dateIndex]);
      if (!Number.isNaN(parsedDate.getTime())) {
          masterRowOutput[dateIndex] = parsedDate;
      } else {
          masterRowOutput[dateIndex] = ''; // Fallback to blank if parsing fails
          Logger.log(`Warning: Could not parse date "${sourceRowData[DATE_COL_INDEX - 1]}" for UUID ${uuid} during build.`);
      }
  } else if (dateIndex !== -1 && !masterRowOutput[dateIndex]){
     masterRowOutput[dateIndex] = ''; // Ensure blank if original was blank/invalid
  }


  return masterRowOutput;
}

/**
 * Safely assigns a value to the correct position in the output row array based on the header name.
 * Finds the index of the header name (case-insensitive) and places the value there.
 * Converts null or undefined values to empty strings.
 * If the header name is not found, it logs a warning but does not throw an error.
 *
 * @param {any[]} outputRow The array representing the row being built.
 * @param {string[]} headers The array of header names from the master sheet.
 * @param {string} headerName The name of the header column to assign the value to.
 * @param {*} value The value to assign.
 */
function assignValue_(outputRow, headers, headerName, value) {
  const lowerCaseHeaderName = headerName.toLowerCase();
  const index = headers.findIndex(h => h.toString().toLowerCase() === lowerCaseHeaderName);
  if (index !== -1) {
    // Store null/undefined as blanks, otherwise use the value
    outputRow[index] = (value === null || value === undefined) ? '' : value;
  } else {
    Logger.log(`Warning: Header "${headerName}" not found in master sheet headers during row construction.`);
  }
}