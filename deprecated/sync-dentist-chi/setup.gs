/**
 * @fileoverview Setup module for the CHI Dentist Sync application.
 * 
 * This module handles all setup and initialization procedures, including:
 * - Initial spreadsheet configuration and formatting
 * - Creation of necessary sheets and templates
 * - Validation of required resources and permissions
 * - Installation of necessary triggers
 * - First-time setup and onboarding processes
 * 
 * The setup module ensures that the application environment is properly
 * configured before any synchronization operations are performed, reducing
 * the risk of errors during normal operation.
 */

/**
 * Performs initial setup tasks:
 * - Ensures the UUID column exists in all valid monthly source sheets.
 * - Seeds UUIDs for any existing rows that are missing them and have valid data.
 * - Ensures the log sheet exists in the master sheet.
 * - Ensures master sheet headers are correct.
 * - Sets up the time-driven trigger for the main sync function.
 * - Sets up the on-edit trigger for the source sheet.
 * Logs success or failure to the Apps Script Logger and notifies via email on failure.
 */
function Setup() {
  const functionName = SETUP_FUNCTION_NAME;
  try {
    Logger.log(`Starting ${functionName} for ${PROVIDER_NAME}...`);
    ensureUuidColumnForAllMonths_();
    seedAllMissingUuids_();
    ensureLogSheet_();
    ensureMasterHeaders_();
    reinstallTrigger_(SYNC_FUNCTION_NAME, 6);
    reinstallEditTrigger_();
    Logger.log(`${functionName} completed successfully.`);
    SpreadsheetApp.getUi().alert(`${PROVIDER_NAME} Sync Setup Successful! See Execution Logs for details.`);
  } catch (err) {
    Logger.log(`${functionName} failed: ${err.message}\n${err.stack}`);
    notifyError_(functionName, err);
    SpreadsheetApp.getUi().alert(`${PROVIDER_NAME} Sync Setup FAILED! Check Execution Logs and email for details.`);
  }
}

/**
 * Ensures the UUID column exists and has the correct header ('UUID') in all valid monthly sheets
 * within the source spreadsheet. Inserts the column at UUID_COL_INDEX if missing.
 */
function ensureUuidColumnForAllMonths_() {
  Logger.log(`Ensuring UUID column (Index: ${UUID_COL_INDEX}) exists in source sheets...`);
  const ss = SpreadsheetApp.openById(SOURCE_SHEET_ID);
  if (!ss) {
      throw new Error(`Cannot ensure UUID columns: Source Spreadsheet not found or inaccessible (ID: ${SOURCE_SHEET_ID})`);
  }
  const sheets = ss.getSheets();

  for (const sheet of sheets) {
    const sheetName = sheet.getName();
    if (!isMonthlySheet_(sheetName)) continue;

    const lastCol = sheet.getLastColumn();
    let uuidHeader = '';

    // Ensure header row is valid before trying to read/write
     if (SOURCE_HEADER_ROW <= 0 || SOURCE_HEADER_ROW > sheet.getMaxRows()) {
        Logger.log(`Skipping UUID column check for sheet "${sheetName}": Invalid SOURCE_HEADER_ROW (${SOURCE_HEADER_ROW}).`);
        continue;
    }

    // Check if UUID_COL_INDEX is within current sheet bounds before trying to read header
    if (lastCol >= UUID_COL_INDEX) {
       try {
         uuidHeader = sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).getValue().toString().trim();
       } catch (e) {
         Logger.log(`Warning: Could not read header at [${SOURCE_HEADER_ROW}, ${UUID_COL_INDEX}] in sheet "${sheetName}": ${e.message}`);
       }
    }

    // If column doesn't exist or header is wrong/missing
    if (lastCol < UUID_COL_INDEX || uuidHeader.toLowerCase() !== 'uuid') {
       Logger.log(`UUID column issue detected in "${sheetName}". Attempting fix (Target Col: ${UUID_COL_INDEX})...`);
       try {
         // Case 1: Column exists, but header is wrong/missing
         if (lastCol >= UUID_COL_INDEX) {
            sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).setValue('UUID');
            Logger.log(`Updated header in column ${UUID_COL_INDEX} to "UUID" in sheet "${sheetName}".`);
         }
         // Case 2: Column needs to be inserted
         else {
            const colsToAdd = UUID_COL_INDEX - lastCol;
            // Check if inserting exactly at lastCol + 1, or further out
             if (UUID_COL_INDEX === lastCol + 1) {
                 sheet.insertColumnAfter(lastCol);
                 Logger.log(`Inserted 1 column after column ${lastCol} in sheet "${sheetName}".`);
             } else {
                 // Insert multiple columns *after* the current last column
                 sheet.insertColumnsAfter(lastCol, colsToAdd);
                 Logger.log(`Inserted ${colsToAdd} column(s) after column ${lastCol} in sheet "${sheetName}".`);
             }

            // Now set the header in the newly ensured column UUID_COL_INDEX
            sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).setValue('UUID');
            Logger.log(`Set header "UUID" in new column ${UUID_COL_INDEX} in sheet "${sheetName}".`);
         }
         SpreadsheetApp.flush(); // Apply changes
       } catch (e) {
          Logger.log(`ERROR trying to add/update UUID column in sheet "${sheetName}": ${e.message}. Skipping sheet.`);
          notifyError_('ensureUuidColumnForAllMonths_', e, `Failed for sheet: ${sheetName}`);
          // Continue to next sheet instead of throwing to allow setup to partially succeed
       }
    }
  } // End loop
  Logger.log('UUID column check completed.');
}

/**
 * Ensures the log sheet exists in the master spreadsheet with the correct headers.
 * Creates the sheet if it doesn't exist.
 */
function ensureLogSheet_() {
  Logger.log(`Ensuring log sheet "${LOG_TAB_NAME}" exists in master (${MASTER_SPREADSHEET_ID})...`);
  const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
   if (!ss) {
      throw new Error(`Cannot ensure log sheet: Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);
  }
  let logSheet = ss.getSheetByName(LOG_TAB_NAME);

  // Added 'Skipped (Invalid Data)' column
  const expectedHeaders = [
    'Timestamp', 'Status', 'Rows Inspected', 'Skipped (No UUID)',
    'Skipped (Duplicate)', 'Skipped (Invalid Data)', 'Rows Added', 'Duration (s)', 'Error Message'
  ];

  if (!logSheet) {
    logSheet = ss.insertSheet(LOG_TAB_NAME);
    Logger.log(`Created log sheet "${LOG_TAB_NAME}".`);
    logSheet.appendRow(expectedHeaders);
    logSheet.setFrozenRows(1);
    logSheet.getRange(1, 1, 1, expectedHeaders.length).setFontWeight("bold");
    try {
        logSheet.setColumnWidths(1, expectedHeaders.length, 150); // Adjust width if needed
    } catch (e) {
        Logger.log(`Warning: Could not set column widths for log sheet "${LOG_TAB_NAME}". ${e.message}`);
    }
  } else {
    // Verify headers if sheet exists
    let headersOk = false;
    const lastLogCol = logSheet.getLastColumn();
    if (logSheet.getLastRow() >= 1 && lastLogCol > 0) {
        // Read only up to the number of expected headers or last column, whichever is smaller
        const colsToRead = Math.min(expectedHeaders.length, lastLogCol);
        const headerRange = logSheet.getRange(1, 1, 1, colsToRead);
        const currentHeaders = headerRange.getValues()[0];

        // Compare length first, then content if lengths match
        headersOk = currentHeaders.length === expectedHeaders.length &&
                    currentHeaders.every((val, index) => val === expectedHeaders[index]);
    }

    if (!headersOk) {
      Logger.log(`Log sheet "${LOG_TAB_NAME}" found, but headers are missing or incorrect. Resetting headers.`);
      // Ensure enough columns exist
      if(logSheet.getMaxColumns() < expectedHeaders.length) {
        logSheet.insertColumnsAfter(logSheet.getMaxColumns(), expectedHeaders.length - logSheet.getMaxColumns());
      }
      // Clear existing header range and set new ones
      logSheet.getRange(1, 1, 1, logSheet.getMaxColumns()).clearContent();
      logSheet.getRange(1, 1, 1, expectedHeaders.length).setValues([expectedHeaders]).setFontWeight("bold");
      logSheet.setFrozenRows(1);
      try {
        logSheet.setColumnWidths(1, expectedHeaders.length, 150);
      } catch (e) {
        Logger.log(`Warning: Could not set column widths for log sheet "${LOG_TAB_NAME}" after resetting headers. ${e.message}`);
      }
      // Optional: Clear old data below incorrect headers?
      // if (logSheet.getLastRow() > 1) {
      //    logSheet.getRange(2, 1, logSheet.getLastRow()-1, expectedHeaders.length).clearContent();
      // }
    }
  }
  Logger.log(`Log sheet "${LOG_TAB_NAME}" is ready.`);
}

/**
 * Ensures the master "Data" tab has the correct header row.
 * Overwrites row 1 with the expected column names defined within the function.
 */
function ensureMasterHeaders_() {
  Logger.log(`Ensuring headers in master sheet tab: "${MASTER_TAB_NAME}"...`);
  const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
  const sh = ss.getSheetByName(MASTER_TAB_NAME);
  if (!sh) {
    throw new Error(`ensureMasterHeaders_: Tab "${MASTER_TAB_NAME}" not found in master sheet (${MASTER_SPREADSHEET_ID}).`);
  }
  // Define the canonical master headers
  const expected = [
    'date', 'hours_worked', 'location', 'provider_name', 'provider_type',
    'source_sheet', 'production_goal_daily', 'verified_production', 'bonus',
    'humble_production', 'baytown_production', 'monthly_goal', 'external_id', 'uuid'
  ];

  const lastCol = sh.getLastColumn();
  let currentHeaders = [];
  if (sh.getLastRow() >= 1 && lastCol > 0) {
      currentHeaders = sh.getRange(1, 1, 1, lastCol).getValues()[0];
  }

  // Check if headers match exactly (order and content)
  const headersMatch = currentHeaders.length === expected.length &&
                       currentHeaders.every((h, i) => h === expected[i]);

  if (!headersMatch) {
      Logger.log('Master headers are incorrect or missing. Resetting headers...');
      // Ensure enough columns exist
      if (lastCol < expected.length) {
        sh.insertColumnsAfter(lastCol, expected.length - lastCol);
      } else if (lastCol > expected.length) {
        // Optionally delete extra columns, or just overwrite
        // sh.deleteColumns(expected.length + 1, lastCol - expected.length);
      }
      // Overwrite the first row
      sh.getRange(1, 1, 1, expected.length).setValues([expected]);
      sh.setFrozenRows(1); // Re-apply frozen row
      sh.getRange(1, 1, 1, expected.length).setFontWeight("bold"); // Re-apply bold
      Logger.log('Master headers reset.');
  } else {
      Logger.log('Master headers are correct.');
  }
}

/**
 * Seeds UUID values (in UUID_COL_INDEX) for all rows in monthly sheets
 * that are missing them but have valid data (Date, Production).
 * Skips rows with future dates or no valid production data.
 * Typically run once during initial setup.
 */
function seedAllMissingUuids_() {
  Logger.log(`Starting UUID seeding for ${PROVIDER_NAME} monthly sheets...`);
  const ss = SpreadsheetApp.openById(SOURCE_SHEET_ID);
  if (!ss) {
    throw new Error(`Cannot seed UUIDs: Source Spreadsheet (${SOURCE_SHEET_ID}) not found or inaccessible.`);
  }
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Normalize

  let totalSeeded = 0;
  const sheets = ss.getSheets();

  for (const sh of sheets) {
    const sheetName = sh.getName();
    if (!isMonthlySheet_(sheetName)) continue;

    Logger.log(`Seeding UUIDs in sheet: "${sheetName}"`);
    const lastRow = sh.getLastRow();
    if (lastRow < SOURCE_DATA_START_ROW) {
      Logger.log(`Skipping "${sheetName}" - no data rows found.`);
      continue;
    }

    // Determine the max column index needed for reading checks + UUID
    const maxColIndex = Math.max(UUID_COL_INDEX, DATE_COL_INDEX, HUMBLE_PROD_COL_INDEX, BAYTOWN_PROD_COL_INDEX);
    const numRows = lastRow - SOURCE_DATA_START_ROW + 1;
    if (numRows <= 0) continue;

    // --- Read relevant columns (Date, Humble Prod, Baytown Prod, UUID) ---
    const dataRange = sh.getRange(SOURCE_DATA_START_ROW, 1, numRows, maxColIndex);
    const values = dataRange.getValues();
    let sheetSeeded = 0;
    let changesMade = false; // Flag to optimize writing

    for (let i = 0; i < values.length; i++) {
      const currentRow = values[i];
      const uuid = currentRow[UUID_COL_INDEX - 1]; // 0-based

      // Only proceed if UUID is missing
      if (!uuid) {
          const dateValue = currentRow[DATE_COL_INDEX - 1];
          const humbleProd = currentRow[HUMBLE_PROD_COL_INDEX - 1];
          const baytownProd = currentRow[BAYTOWN_PROD_COL_INDEX - 1];

          // --- Perform Validation Checks ---
          // 1. Check Date validity
          if (!dateValue || !(dateValue instanceof Date) || Number.isNaN(dateValue.getTime())) {
              continue; // Skip if date is invalid/blank
          }
          // 2. Check Date is not in the future
          const sourceDate = new Date(dateValue);
          sourceDate.setHours(0, 0, 0, 0);
          if (sourceDate > today) {
              continue; // Skip if date is in the future
          }
          // 3. Check for *at least one* valid production value
          const hasValidProduction = (typeof humbleProd === 'number' && !Number.isNaN(humbleProd)) ||
                                     (typeof baytownProd === 'number' && !Number.isNaN(baytownProd));
          if (!hasValidProduction) {
             continue; // Skip if no valid production data
          }
          // --- End Checks ---

          // If all checks pass, generate and assign UUID to the array
          currentRow[UUID_COL_INDEX - 1] = Utilities.getUuid();
          sheetSeeded++;
          changesMade = true;
      } // End if(!uuid)
    } // End loop through rows

    // Write back to the sheet ONLY if changes were made
    if (changesMade) {
      // Get the range specifically for the UUID column to write back to
      const uuidColumnRange = sh.getRange(SOURCE_DATA_START_ROW, UUID_COL_INDEX, numRows, 1);
      // Extract just the UUID column from the potentially modified 'values' array
      const uuidValuesToWrite = values.map(row => [row[UUID_COL_INDEX - 1]]);
      uuidColumnRange.setValues(uuidValuesToWrite);

      totalSeeded += sheetSeeded;
      Logger.log(`Added ${sheetSeeded} UUIDs to sheet "${sheetName}"`);
    } else {
      Logger.log(`No missing UUIDs requiring seeding found in sheet "${sheetName}"`);
    }
    SpreadsheetApp.flush(); // Ensure writes are committed before next sheet
  } // End loop through sheets

  Logger.log(`UUID seeding completed for ${PROVIDER_NAME}. Added ${totalSeeded} UUIDs across all valid monthly sheets.`);
}

/**
 * Deletes existing time-driven triggers for a specified handler function and creates a new one.
 *
 * @param {string} functionName The name of the function to trigger (e.g., 'syncToExternalMaster').
 * @param {number} hours The frequency in hours. Throws error if invalid.
 */
function reinstallTrigger_(functionName, hours) {
  // Check if the function exists in the global scope (tricky in Apps Script, basic check)
  if (typeof this[functionName] !== 'function') {
      throw new Error(`Trigger installation failed: Function "${functionName}" does not seem to exist or is not global.`);
  }
  if (typeof hours !== 'number' || hours <= 0 || !Number.isInteger(hours * 60)) {
     throw new Error(`Trigger installation failed: Invalid hours value "${hours}". Must be a positive number resulting in whole minutes.`);
  }

  deleteTriggersByHandler_(functionName); // Delete existing triggers first
  ScriptApp.newTrigger(functionName)
    .timeBased()
    .everyHours(hours)
    .create();
  Logger.log(`Installed time-driven trigger for "${functionName}" to run every ${hours} hours.`);
}

/**
 * Deletes existing edit triggers for the defined EDIT_HANDLER_FUNCTION_NAME
 * and creates a new onEdit trigger bound to the SOURCE_SHEET_ID.
 */
function reinstallEditTrigger_() {
   // Use the constant defined in the configuration section
   const functionName = EDIT_HANDLER_FUNCTION_NAME;
    if (typeof this[functionName] !== 'function') {
      throw new Error(`Trigger installation failed: Function "${functionName}" does not seem to exist or is not global.`);
   }
   // Delete existing triggers for this handler first
   deleteTriggersByHandler_(functionName); // Use constant

   // Create a new specific trigger for the source spreadsheet
   ScriptApp.newTrigger(functionName) // Use constant
     .forSpreadsheet(SOURCE_SHEET_ID) // Bind to THIS provider's sheet
     .onEdit()
     .create();
   Logger.log(`Installed onEdit trigger for "${functionName}" on Spreadsheet ID ${SOURCE_SHEET_ID}.`);
}

/**
 * Deletes all triggers associated with a specific handler function name for the current script project.
 *
 * @param {string} functionName The handler function name (e.g., 'syncToExternalMaster', 'onEditHandler').
 */
function deleteTriggersByHandler_(functionName) {
  let deletedCount = 0;
  try {
      const triggers = ScriptApp.getProjectTriggers();
      for (const trigger of triggers) {
        if (trigger.getHandlerFunction() === functionName) {
          try {
             ScriptApp.deleteTrigger(trigger);
             deletedCount++;
          } catch (e) {
             Logger.log(`Warning: Failed to delete trigger for "${functionName}" (ID: ${trigger.getUniqueId()}): ${e.message}`);
          }
        }
      }
  } catch (e) {
      Logger.log(`Error accessing project triggers for deletion: ${e.message}. Manual check might be needed.`);
      // Optionally notify here if access fails
      return;
  }

  if (deletedCount > 0) {
     Logger.log(`Deleted ${deletedCount} existing trigger(s) for handler function "${functionName}".`);
  } else {
     // This is normal if run for the first time or after manual deletion
     // Logger.log(`No existing triggers found for handler function "${functionName}".`);
  }
}