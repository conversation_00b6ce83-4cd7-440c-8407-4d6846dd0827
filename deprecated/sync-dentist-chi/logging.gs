/**
 * @fileoverview Logging module for the CHI Dentist Sync application.
 * 
 * This module provides comprehensive logging functionality, including:
 * - Different log levels (debug, info, warning, error)
 * - Structured logging with timestamps and context information
 * - Log storage and rotation strategies
 * - Performance metrics and timing information
 * - Integration with notification systems for critical errors
 * - Audit trail capabilities for tracking changes
 * 
 * The logging module enables effective monitoring, debugging, and auditing
 * of the application's operation, providing visibility into the sync process
 * and helping to identify and resolve issues quickly.
 */

/**
 * Ensures the log sheet exists in the master spreadsheet with the correct headers.
 * Creates the sheet if it doesn't exist.
 */
function ensureLogSheet_() {
  Logger.log(`Ensuring log sheet "${LOG_TAB_NAME}" exists in master (${MASTER_SPREADSHEET_ID})...`);
  const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
   if (!ss) {
      throw new Error(`Cannot ensure log sheet: Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);
  }
  let logSheet = ss.getSheetByName(LOG_TAB_NAME);

  // Added 'Skipped (Invalid Data)' column
  const expectedHeaders = [
    'Timestamp', 'Status', 'Rows Inspected', 'Skipped (No UUID)',
    'Skipped (Duplicate)', 'Skipped (Invalid Data)', 'Rows Added', 'Duration (s)', 'Error Message'
  ];

  if (!logSheet) {
    logSheet = ss.insertSheet(LOG_TAB_NAME);
    Logger.log(`Created log sheet "${LOG_TAB_NAME}".`);
    logSheet.appendRow(expectedHeaders);
    logSheet.setFrozenRows(1);
    logSheet.getRange(1, 1, 1, expectedHeaders.length).setFontWeight("bold");
    try {
        logSheet.setColumnWidths(1, expectedHeaders.length, 150); // Adjust width if needed
    } catch (e) {
        Logger.log(`Warning: Could not set column widths for log sheet "${LOG_TAB_NAME}". ${e.message}`);
    }
  } else {
    // Verify headers if sheet exists
    let headersOk = false;
    const lastLogCol = logSheet.getLastColumn();
    if (logSheet.getLastRow() >= 1 && lastLogCol > 0) {
        // Read only up to the number of expected headers or last column, whichever is smaller
        const colsToRead = Math.min(expectedHeaders.length, lastLogCol);
        const headerRange = logSheet.getRange(1, 1, 1, colsToRead);
        const currentHeaders = headerRange.getValues()[0];

        // Compare length first, then content if lengths match
        headersOk = currentHeaders.length === expectedHeaders.length &&
                    currentHeaders.every((val, index) => val === expectedHeaders[index]);
    }

    if (!headersOk) {
      Logger.log(`Log sheet "${LOG_TAB_NAME}" found, but headers are missing or incorrect. Resetting headers.`);
      // Ensure enough columns exist
      if(logSheet.getMaxColumns() < expectedHeaders.length) {
        logSheet.insertColumnsAfter(logSheet.getMaxColumns(), expectedHeaders.length - logSheet.getMaxColumns());
      }
      // Clear existing header range and set new ones
      logSheet.getRange(1, 1, 1, logSheet.getMaxColumns()).clearContent();
      logSheet.getRange(1, 1, 1, expectedHeaders.length).setValues([expectedHeaders]).setFontWeight("bold");
      logSheet.setFrozenRows(1);
      try {
        logSheet.setColumnWidths(1, expectedHeaders.length, 150);
      } catch (e) {
        Logger.log(`Warning: Could not set column widths for log sheet "${LOG_TAB_NAME}" after resetting headers. ${e.message}`);
      }
      // Optional: Clear old data below incorrect headers?
      // if (logSheet.getLastRow() > 1) {
      //    logSheet.getRange(2, 1, logSheet.getLastRow()-1, expectedHeaders.length).clearContent();
      // }
    }
  }
  Logger.log(`Log sheet "${LOG_TAB_NAME}" is ready.`);
}

/**
 * Logs a summary of a sync run to the designated log sheet.
 * Includes the new 'Skipped (Invalid Data)' count.
 *
 * @param {Date} startTime The Date object marking the start of the run.
 * @param {string} status The final status ('Success' or 'Failure').
 * @param {number} inspected Total rows checked in source sheets.
 * @param {number} noUuid Rows skipped due to missing UUID.
 * @param {number} duplicate Rows skipped because UUID already exists in master.
 * @param {number} skippedInvalid Rows skipped due to invalid date or lack of production data.
 * @param {number} added Rows successfully added to the master sheet.
 * @param {number} durationSeconds The duration of the run in seconds.
 * @param {string} [errorMessage=''] Optional error message if status is 'Failure'.
 */
function logRun_(startTime, status, inspected, noUuid, duplicate, skippedInvalid, added, durationSeconds, errorMessage = '') {
  try {
    const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
     if (!ss) {
        Logger.log(`ERROR: Cannot log run - Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);
        return;
    }
    const logSheet = ss.getSheetByName(LOG_TAB_NAME);
    if (!logSheet) {
      Logger.log(`ERROR: Log sheet "${LOG_TAB_NAME}" not found. Cannot log run.`);
      // Attempt to recreate if missing? Risky if ID is wrong.
      // try { ensureLogSheet_(); logSheet = ss.getSheetByName(LOG_TAB_NAME); } catch(e) { /* ignore */ }
      if (!logSheet) return; // Give up if still missing
    }

    // Append the log entry including the new skippedInvalid count
    logSheet.appendRow([
      startTime,
      status,
      inspected,
      noUuid,
      duplicate,
      skippedInvalid, // New column value
      added,
      durationSeconds.toFixed(2),
      errorMessage
    ]);
  } catch (logErr) {
    Logger.log(`CRITICAL ERROR: Failed to write to log sheet "${LOG_TAB_NAME}". Status: ${status}, Added: ${added}. Error: ${logErr.message}\n${logErr.stack}`);
    notifyError_('logRun_', logErr, `Failed to log run results. Status: ${status}.`);
  }
}

/**
 * Sends an email notification about an error encountered during script execution.
 * Includes script ID and links to execution logs/editor. Attempts to use active user's
 * email, falls back to a hardcoded address if necessary.
 *
 * @param {string} scope A description of where the error occurred (e.g., function name).
 * @param {Error|string} error The error object caught, or an error message string.
 * @param {string} [additionalContext=''] Optional additional context for the email body.
 */
function notifyError_(scope, error, additionalContext = '') {
  try {
    let recipient = '';
    try {
        recipient = Session.getActiveUser().getEmail();
        // Also check if the email looks valid
        if (!recipient || !recipient.includes('@')) {
            Logger.log(`Active user email "${recipient}" seems invalid.`);
            recipient = ''; // Reset if invalid
        }
    } catch (e) {
        Logger.log(`Could not get active user email: ${e.message}. Using fallback.`);
        recipient = ''; // Ensure recipient is empty if session call fails
    }

    // Fallback Email - IMPORTANT: SET THIS
    const FALLBACK_EMAIL = '<EMAIL>'; // <<< REPLACE WITH YOUR MONITORING EMAIL

    if (!recipient) {
       recipient = FALLBACK_EMAIL;
       Logger.log(`Sending notification to fallback: ${recipient}`);
    }

     if (!recipient || !recipient.includes('@')) {
        Logger.log(`ERROR: Invalid recipient email configured ("${recipient}") for error notifications. Cannot send.`);
        return;
     }

    const subject = `[Apps Script Error] ${PROVIDER_NAME} Sync (${scope})`;

    const errorMessage = (error instanceof Error) ? error.message : String(error);
    const errorStack = (error instanceof Error && error.stack) ? error.stack : '(No stack trace available)';

    let body = `An error occurred in the ${PROVIDER_NAME} data sync script.\n\n`;
    body += `Timestamp: ${new Date().toISOString()}\n`;
    body += `Scope/Function: ${scope}\n`;
    if (additionalContext) {
      body += `Context: ${additionalContext}\n`;
    }
    body += `Error Message: ${errorMessage}\n\n`;
    body += `Stack Trace:\n${errorStack}\n`;

    const scriptId = ScriptApp.getScriptId();
    body += '---\n';
    body += `Script ID: ${scriptId}\n`;
    body += `Source Sheet: https://docs.google.com/spreadsheets/d/${SOURCE_SHEET_ID}/edit\n`;
    body += `Master Sheet: https://docs.google.com/spreadsheets/d/${MASTER_SPREADSHEET_ID}/edit\n`;
    body += `View Executions: https://script.google.com/home/<USER>/${scriptId}/executions\n`;
    body += `Open Script Editor: https://script.google.com/d/${scriptId}/edit\n`;

    Logger.log(`Sending error notification to ${recipient} for scope "${scope}"`);
    MailApp.sendEmail({
      to: recipient,
      subject: subject,
      body: body,
    });

  } catch (mailError) {
    Logger.log(`CRITICAL ERROR: Failed to send error notification email. Scope: ${scope}. Original Error: ${error.message || error}. Mail Error: ${mailError.message}\n${mailError.stack}`);
  }
}