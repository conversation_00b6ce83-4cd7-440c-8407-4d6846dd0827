/**
 * @fileoverview Utility functions for the Kia hygienist data sync script.
 */

/**
 * Checks if a sheet name matches the expected monthly format (e.g., "Jan 2024", "Feb-24").
 * Uses a standard regex, adjust if needed for specific provider formats.
 *
 * @param {string} name The sheet name to test.
 * @returns {boolean} True if the name matches the pattern, false otherwise.
 */
function isMonthlySheet_(name) {
  if (!name || typeof name !== 'string') return false;
  // Regex: Month name (3 letters) + optional separator (space or hyphen) + year (2 or 4 digits)
  const monthlySheetRegex = /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[-\s]?\d{2,4}$/i;
  return monthlySheetRegex.test(name.trim());
}

/**
 * Ensures the UUID column exists and has the correct header in all valid monthly sheets
 * within the source spreadsheet. Inserts the column if missing.
 */
function ensureUuidColumnForAllMonths_() {
  Logger.log('Ensuring UUID column exists in source sheets...');
  const ss = SpreadsheetApp.openById(SOURCE_SHEET_ID);
  if (!ss) {
      throw new Error(`Cannot ensure UUID columns: Source Spreadsheet not found or inaccessible (ID: ${SOURCE_SHEET_ID})`);
  }
  for (const sheet of ss.getSheets()) { // Use for...of
    const sheetName = sheet.getName();
    if (!isMonthlySheet_(sheetName)) continue; // Skip non-monthly sheets

    const lastCol = sheet.getLastColumn();
    let uuidHeader = '';
    // Check if UUID_COL_INDEX is within current sheet bounds before trying to read header
    if (lastCol >= UUID_COL_INDEX && SOURCE_HEADER_ROW <= sheet.getLastRow() && SOURCE_HEADER_ROW > 0) {
       try {
         uuidHeader = sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).getValue().toString().trim();
       } catch (e) {
         Logger.log(`Warning: Could not read header at [${SOURCE_HEADER_ROW}, ${UUID_COL_INDEX}] in sheet "${sheetName}": ${e.message}`);
         // Continue, as the column might need inserting anyway
       }
    }

    // If column doesn't exist or header is wrong/missing
    if (lastCol < UUID_COL_INDEX || uuidHeader.toLowerCase() !== 'uuid') {
      // Ensure SOURCE_HEADER_ROW is valid before trying to write to it
      if (SOURCE_HEADER_ROW <= 0) {
          Logger.log(`Skipping UUID column check for sheet "${sheetName}": SOURCE_HEADER_ROW (${SOURCE_HEADER_ROW}) is invalid.`);
          return;
      }
       // Ensure the sheet has enough rows for the header
       if (sheet.getMaxRows() < SOURCE_HEADER_ROW) {
           sheet.insertRowsAfter(sheet.getMaxRows(), SOURCE_HEADER_ROW - sheet.getMaxRows());
           Logger.log(`Inserted rows in sheet "${sheetName}" to accommodate header row ${SOURCE_HEADER_ROW}.`);
       }

      try {
        // Check if the column index exists but needs header update, or needs insertion
        if (lastCol >= UUID_COL_INDEX) {
           sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).setValue('UUID');
           Logger.log(`Updated header in column ${UUID_COL_INDEX} to "UUID" in sheet "${sheetName}".`);
        } else {
           // Need to insert columns up to the required index if it's far beyond the last column
           if (UUID_COL_INDEX > 1) {
              // Ensure the preceding column exists before inserting *after* it
              if (lastCol < UUID_COL_INDEX - 1) {
                 // Insert multiple columns if needed - more robust
                 const colsToAdd = UUID_COL_INDEX - lastCol;
                 sheet.insertColumnsAfter(lastCol, colsToAdd);
                 Logger.log(`Inserted ${colsToAdd} column(s) after column ${lastCol} in sheet "${sheetName}".`);
              } else {
                // Insert just one column after the preceding one
                sheet.insertColumnAfter(UUID_COL_INDEX - 1);
                Logger.log(`Inserted column after column ${UUID_COL_INDEX - 1} in sheet "${sheetName}".`);
              }
              // Now set the header in the newly ensured column UUID_COL_INDEX
              sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).setValue('UUID');
              Logger.log(`Set header "UUID" in new column ${UUID_COL_INDEX} in sheet "${sheetName}".`);

           } else { // UUID_COL_INDEX is 1
              sheet.insertColumnBefore(1);
              sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).setValue('UUID'); // UUID_COL_INDEX is 1 here
              Logger.log(`Inserted UUID column (1) and header in sheet "${sheetName}".`);
           }
        }
        SpreadsheetApp.flush(); // Apply changes immediately if needed, though often not necessary here
      } catch (e) {
         Logger.log(`Error trying to add/update UUID column in sheet "${sheetName}": ${e.message}`);
         // Decide if this should throw and stop setup, or just log and continue
         // Consider adding notifyError_ here if this failure is critical
      }
    }
  } // End for...of loop
  Logger.log('UUID column check completed.');
}

/**
 * Ensures the master "Data" tab has the correct header row.
 * Overwrites row 1 with the expected column names.
 */
function ensureMasterHeaders_() {
  const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
  const sh = ss.getSheetByName(MASTER_TAB_NAME);
  if (!sh) {
    throw new Error(`ensureMasterHeaders_: Tab "${MASTER_TAB_NAME}" not found.`);
  }
  const expected = [
    'date',
    'hours_worked',
    'location',
    'provider_name',
    'provider_type',
    'source_sheet',
    'production_goal_daily',
    'verified_production',
    'bonus',
    'humble_production',
    'baytown_production',
    'monthly_goal',
    'external_id',
    'uuid'
  ];
  // Ensure enough columns exist
  if (sh.getLastColumn() < expected.length) {
    sh.insertColumnsAfter(sh.getLastColumn(), expected.length - sh.getLastColumn());
  }
  sh.getRange(1, 1, 1, expected.length).setValues([expected]);
}

/**
 * Seeds UUID values for all rows in monthly sheets that are missing them.
 * This is typically run once during initial setup to ensure all existing
 * rows have UUIDs before starting the sync process.
 */
function seedAllMissingUuids_() {
  Logger.log(`Starting UUID seeding for ${PROVIDER_NAME} monthly sheets...`);
  const ss = SpreadsheetApp.openById(SOURCE_SHEET_ID);
  if (!ss) {
    throw new Error(`Cannot seed UUIDs: Source Spreadsheet not found or inaccessible (ID: ${SOURCE_SHEET_ID})`);
  }
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Normalize today

  let totalSeeded = 0;
  for (const sh of ss.getSheets()) {
    const sheetName = sh.getName();
    if (!isMonthlySheet_(sheetName)) continue;
    
    Logger.log(`Processing sheet: "${sheetName}"`);
    const lastRow = sh.getLastRow();
    if (lastRow < SOURCE_DATA_START_ROW) {
      Logger.log(`Skipping sheet "${sheetName}" - no data rows found.`);
      continue;
    }

    // --- Read Date (Col A), Verified Prod (Col D), and UUID (Col R) --- 
    // Determine the max column index needed
    const maxColIndex = Math.max(1, VER_PROD_COL_INDEX, UUID_COL_INDEX);
    const numRows = lastRow - SOURCE_DATA_START_ROW + 1;
    if (numRows <= 0) continue; // Skip if no data rows calculated

    const dataRange = sh.getRange(SOURCE_DATA_START_ROW, 1, numRows, maxColIndex);
    const values = dataRange.getValues();
    let sheetSeeded = 0;
    let changesMade = false; // Flag to track if we need to write back

    // --- Get source header to find Date column index reliably ---    
    const sourceHeaders = sh.getRange(SOURCE_HEADER_ROW, 1, 1, sh.getLastColumn()).getValues()[0];
    const dateColIndexSource = sourceHeaders.findIndex(h => h.toString().toLowerCase() === 'date'); // 0-based index

    for (let i = 0; i < values.length; i++) {
      const currentRow = values[i];
      const uuid = currentRow[UUID_COL_INDEX - 1]; // 0-based index

      // Only proceed if UUID is missing
      if (!uuid) {
          const dateValue = (dateColIndexSource !== -1) ? currentRow[dateColIndexSource] : null; // 0-based index for Date
          const verifiedProd = currentRow[VER_PROD_COL_INDEX - 1]; // 0-based index for Verified Production

          // --- Perform Checks ---
          // 1. Check Date validity and if it's in the future
          if (dateValue === null || dateValue === '' || !(dateValue instanceof Date) || Number.isNaN(dateValue.getTime())) {
              continue; // Skip if date is invalid/blank
          }
          const sourceDate = new Date(dateValue);
          sourceDate.setHours(0, 0, 0, 0);
          if (sourceDate > today) {
              continue; // Skip if date is in the future
          }

          // 2. Check if Verified Production is blank
          if (verifiedProd === null || verifiedProd === undefined || verifiedProd === '') {
             continue; // Skip if verified production is blank
          }
          // --- End Checks ---

          // If all checks pass, generate and assign UUID
          currentRow[UUID_COL_INDEX - 1] = Utilities.getUuid();
          sheetSeeded++;
          changesMade = true;
      } // End if(!uuid)
    } // End loop through rows

    if (changesMade) {
      // Write the modified values back to the original range
      dataRange.setValues(values);
      totalSeeded += sheetSeeded;
      Logger.log(`Added ${sheetSeeded} UUIDs to sheet "${sheetName}"`);
    } else {
      Logger.log(`No missing UUIDs requiring seeding found in sheet "${sheetName}"`);
    }
  } // End loop through sheets
  
  Logger.log(`UUID seeding completed. Added ${totalSeeded} UUIDs across all monthly sheets.`);
}