/**
 * @fileoverview Setup procedures for the Kia hygienist data sync script.
 */

/**
 * Performs initial setup tasks:
 * - Ensures the UUID column exists in all valid monthly source sheets.
 * - Seeds UUIDs for any existing rows that are missing them.
 * - Ensures the log sheet exists in the master sheet.
 * - Sets up the time-driven trigger for the main sync function.
 * - Sets up the on-edit trigger for the source sheet.
 * Logs success or failure to the Apps Script Logger and notifies via email on failure.
 * This function is portable and can be run manually or via trigger.
 */
function Setup() {
  const functionName = SETUP_FUNCTION_NAME; // Use constant
  try {
    Logger.log(`Starting ${functionName}...`);
    ensureUuidColumnForAllMonths_();
    seedAllMissingUuids_();
    ensureLogSheet_();
    ensureMasterHeaders_();
    reinstallTrigger_(SYNC_FUNCTION_NAME, 6); // Use constant // Run sync every 6 hours
    reinstallEditTrigger_(); // Internally uses EDIT_HANDLER_FUNCTION_NAME
    Logger.log(`${functionName} completed successfully.`);
    // --- IMPROVEMENT: Removed SpreadsheetApp.getUi().alert() for portability ---
    // Check Apps Script execution logs (View -> Executions) for success confirmation.
    // ---------------------------------------------------------------------------
  } catch (err) {
    Logger.log(`${functionName} failed: ${err.message}\n${err.stack}`);
    notifyError_(functionName, err);
    // --- IMPROVEMENT: Removed SpreadsheetApp.getUi().alert() for portability ---
    // Failure is logged above and an email notification is sent via notifyError_.
    // Check Apps Script execution logs or email for failure details.
    // ---------------------------------------------------------------------------
  }
}