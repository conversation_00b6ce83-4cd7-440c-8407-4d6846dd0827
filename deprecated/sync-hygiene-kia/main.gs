/**
 * @fileoverview Main entry points and menu setup for the Kia hygienist data sync script.
 */

/**
 * Adds a custom menu to the spreadsheet UI.
 */
function onOpen() {
  SpreadsheetApp.getUi()
    .createMenu(`Sync Tools (${PROVIDER_NAME})`)
    .addItem('Run Full Sync Now', SYNC_FUNCTION_NAME)
    .addSeparator()
    .addItem('Run Setup (Triggers, UUIDs, Logs)', SETUP_FUNCTION_NAME)
    .addItem('Seed Missing UUIDs Only', SEED_UUIDS_FUNCTION_NAME)
    .addToUi();
}