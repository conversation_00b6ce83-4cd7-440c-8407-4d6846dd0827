/**
 * @fileoverview Logging functionality for the Kia hygienist data sync script.
 */

/**
 * Ensures the log sheet exists in the master spreadsheet with the correct headers.
 * Creates the sheet if it doesn't exist.
 */
function ensureLogSheet_() {
  Logger.log(`Ensuring log sheet "${LOG_TAB_NAME}" exists...`);
  const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
   if (!ss) {
      throw new Error(`Cannot ensure log sheet: Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);
  }
  let logSheet = ss.getSheetByName(LOG_TAB_NAME);

  const expectedHeaders = [
    'Timestamp', 'Status', 'Rows Inspected', 'Skipped (No UUID)',
    'Skipped (Duplicate)', 'Rows Added', 'Duration (s)', 'Error Message'
  ];

  if (!logSheet) {
    logSheet = ss.insertSheet(LOG_TAB_NAME);
    Logger.log(`Created log sheet "${LOG_TAB_NAME}".`);
    logSheet.appendRow(expectedHeaders); // appendRow automatically adds row after last content
    logSheet.setFrozenRows(1);
    logSheet.getRange("A1:H1").setFontWeight("bold");
    try { // setColumnWidths can fail if sheet protection exists
        logSheet.setColumnWidths(1, expectedHeaders.length, 150);
    } catch (e) {
        Logger.log(`Warning: Could not set column widths for log sheet "${LOG_TAB_NAME}". ${e.message}`);
    }
  } else {
    // Verify headers if sheet exists
    let headersOk = false;
    if (logSheet.getLastRow() >= 1) { // Check if header row exists
        const headerRange = logSheet.getRange(1, 1, 1, Math.min(expectedHeaders.length, logSheet.getLastColumn()));
        const currentHeaders = headerRange.getValues()[0];
        // Simple length check first
        headersOk = currentHeaders.length === expectedHeaders.length;
        // Deep comparison if length matches
        if (headersOk) {
            headersOk = currentHeaders.every((val, index) => val === expectedHeaders[index]);
        }
    }

    if (!headersOk) {
      Logger.log(`Log sheet "${LOG_TAB_NAME}" found, but headers are missing or incorrect. Resetting headers.`);
      // Ensure enough columns exist before setting values
      if(logSheet.getMaxColumns() < expectedHeaders.length) {
        logSheet.insertColumns(logSheet.getMaxColumns() + 1, expectedHeaders.length - logSheet.getMaxColumns());
      }
      // Clear existing header range and set new ones
      logSheet.getRange(1, 1, 1, logSheet.getMaxColumns()).clearContent(); // Clear entire first row
      logSheet.getRange(1, 1, 1, expectedHeaders.length).setValues([expectedHeaders]);

      logSheet.setFrozenRows(1);
      logSheet.getRange(1, 1, 1, expectedHeaders.length).setFontWeight("bold");
      try { // setColumnWidths can fail if sheet protection exists
        logSheet.setColumnWidths(1, expectedHeaders.length, 150);
      } catch (e) {
        Logger.log(`Warning: Could not set column widths for log sheet "${LOG_TAB_NAME}" after resetting headers. ${e.message}`);
      }
      // Optionally clear old data below incorrect headers:
      // if (logSheet.getLastRow() > 1) {
      //    logSheet.getRange(2, 1, logSheet.getLastRow()-1, expectedHeaders.length).clearContent();
      // }
    }
  }
  Logger.log(`Log sheet "${LOG_TAB_NAME}" is ready.`);
}

/**
 * Logs a summary of a sync run to the designated log sheet.
 *
 * @param {Date} startTime The Date object marking the start of the run.
 * @param {string} status The final status ('Success' or 'Failure').
 * @param {number} inspected Total rows checked in source sheets.
 * @param {number} noUuid Rows skipped due to missing UUID.
 * @param {number} duplicate Rows skipped because UUID already exists in master.
 * @param {number} added Rows successfully added to the master sheet.
 * @param {number} durationSeconds The duration of the run in seconds.
 * @param {string} [errorMessage=''] Optional error message if status is 'Failure'.
 */
function logRun_(startTime, status, inspected, noUuid, duplicate, added, durationSeconds, errorMessage = '') {
  try {
    const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
     if (!ss) {
        Logger.log(`ERROR: Cannot log run - Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);
        return; // Cannot log if master sheet is inaccessible
    }
    const logSheet = ss.getSheetByName(LOG_TAB_NAME);
    if (!logSheet) {
      Logger.log(`ERROR: Log sheet "${LOG_TAB_NAME}" not found. Cannot log run.`);
      // Attempt to recreate log sheet if missing? Could lead to infinite loop if master ID is wrong.
      // try { ensureLogSheet_(); logSheet = ss.getSheetByName(LOG_TAB_NAME); } catch(e) { Logger.log("Failed to recreate log sheet."); return; }
      // If ensureLogSheet failed earlier, logSheet might still be null.
      if (!logSheet) return; // Stop if still missing after potential recreation attempt
    }

    // Append the log entry - use ISOString for date consistency if preferred, or default Date object format
    logSheet.appendRow([
      startTime, //.toISOString(), // Use start time for timestamp consistency
      status,
      inspected,
      noUuid,
      duplicate,
      added,
      durationSeconds.toFixed(2), // Format duration
      errorMessage
    ]);
  } catch (logErr) {
    // If logging itself fails, log to the Apps Script logger
    Logger.log(`CRITICAL ERROR: Failed to write to log sheet "${LOG_TAB_NAME}". Status: ${status}, Added: ${added}. Error: ${logErr.message}\n${logErr.stack}`);
    // Optionally notify about the logging failure itself
    notifyError_('logRun_', logErr, `Failed to log run results. Status: ${status}.`);
  }
}