/**
 * @fileoverview Syncs data from monthly tabs in a source Google Sheet
 * to a central master Google Sheet, ensuring unique entries based on UUIDs.
 * Includes setup for UUID columns, logging, and triggers.
 * Handles onEdit events in the source sheet to generate UUIDs.
 * Provides error notification and execution logging.
 *
 * @version 1.5 - Generalized comments and descriptions.
 */

// This file serves as the main entry point for the Kia hygienist data sync script.
// The actual implementation has been modularized into separate files:
//
// - config.gs: Configuration constants
// - main.gs: Entry points and menu setup
// - setup.gs: Setup procedures
// - sync.gs: Core sync functionality
// - mapping.gs: Data transformation
// - logging.gs: Logging functionality
// - triggers.gs: Trigger management
// - utils.gs: Utility functions
// - notification.gs: Error notification
// - handlers.gs: Event handlers
//
// This modular structure improves maintainability and readability.
