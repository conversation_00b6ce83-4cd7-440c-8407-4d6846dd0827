/**
 * @fileoverview Setup module for the OBI Dentist Sync application.
 * 
 * This module handles all setup and initialization procedures, including:
 * - Initial spreadsheet configuration and formatting
 * - Creation of necessary sheets and templates
 * - Validation of required resources and permissions
 * - Installation of necessary triggers
 * - First-time setup and onboarding processes
 * 
 * The setup module ensures that the application environment is properly
 * configured before any synchronization operations are performed, reducing
 * the risk of errors during normal operation.
 */

/**
 * Performs initial setup tasks:
 * - Ensures the UUID column exists in all valid monthly source sheets.
 * - Seeds UUIDs for any existing rows that are missing them and have valid data.
 * - Ensures the log sheet exists in the master sheet.
 * - Ensures master sheet headers are correct.
 * - Sets up the time-driven trigger for the main sync function.
 * - Sets up the on-edit trigger for the source sheet.
 * Logs success or failure to the Apps Script Logger and notifies via email on failure.
 */
function Setup() {
  const functionName = SETUP_FUNCTION_NAME;
  try {
    Logger.log(`Starting ${functionName} for ${PROVIDER_NAME}...`);
    ensureUuidColumnForAllMonths_();
    seedAllMissingUuids_();
    ensureLogSheet_();
    ensureMasterHeaders_();
    reinstallTrigger_(SYNC_FUNCTION_NAME, 6);
    reinstallEditTrigger_();
    Logger.log(`${functionName} completed successfully.`);
    SpreadsheetApp.getUi().alert(`${PROVIDER_NAME} Sync Setup Successful! See Execution Logs for details.`);
  } catch (err) {
    Logger.log(`${functionName} failed: ${err.message}\n${err.stack}`);
    notifyError_(functionName, err);
    SpreadsheetApp.getUi().alert(`${PROVIDER_NAME} Sync Setup FAILED! Check Execution Logs and email for details.`);
  }
}

/**
 * Ensures the UUID column exists and has the correct header ('UUID') in all valid monthly sheets
 * within the source spreadsheet. Inserts the column at UUID_COL_INDEX if missing.
 */
function ensureUuidColumnForAllMonths_() {
  Logger.log(`Ensuring UUID column (Index: ${UUID_COL_INDEX}) exists in source sheets...`);
  const ss = SpreadsheetApp.openById(SOURCE_SHEET_ID);
  if (!ss) {
      throw new Error(`Cannot ensure UUID columns: Source Spreadsheet not found or inaccessible (ID: ${SOURCE_SHEET_ID})`);
  }
  const sheets = ss.getSheets();

  for (const sheet of sheets) {
    const sheetName = sheet.getName();
    if (!isMonthlySheet_(sheetName)) continue;

    const lastCol = sheet.getLastColumn();
    let uuidHeader = '';

    // Ensure header row is valid before trying to read/write
     if (SOURCE_HEADER_ROW <= 0 || SOURCE_HEADER_ROW > sheet.getMaxRows()) {
        Logger.log(`Skipping UUID column check for sheet "${sheetName}": Invalid SOURCE_HEADER_ROW (${SOURCE_HEADER_ROW}).`);
        continue;
    }

    // Check if UUID_COL_INDEX is within current sheet bounds before trying to read header
    if (lastCol >= UUID_COL_INDEX) {
       try {
         uuidHeader = sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).getValue().toString().trim();
       } catch (e) {
         Logger.log(`Warning: Could not read header at [${SOURCE_HEADER_ROW}, ${UUID_COL_INDEX}] in sheet "${sheetName}": ${e.message}`);
       }
    }

    // If column doesn't exist or header is wrong/missing
    if (lastCol < UUID_COL_INDEX || uuidHeader.toLowerCase() !== 'uuid') {
       Logger.log(`UUID column issue detected in "${sheetName}". Attempting fix (Target Col: ${UUID_COL_INDEX})...`);
       try {
         // Case 1: Column exists, but header is wrong/missing
         if (lastCol >= UUID_COL_INDEX) {
            sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).setValue('UUID');
            Logger.log(`Updated header in column ${UUID_COL_INDEX} to "UUID" in sheet "${sheetName}".`);
         }
         // Case 2: Column needs to be inserted
         else {
            const colsToAdd = UUID_COL_INDEX - lastCol;
            // Check if inserting exactly at lastCol + 1, or further out
             if (UUID_COL_INDEX === lastCol + 1) {
                 sheet.insertColumnAfter(lastCol);
                 Logger.log(`Inserted 1 column after column ${lastCol} in sheet "${sheetName}".`);
             } else {
                 // Insert multiple columns *after* the current last column
                 sheet.insertColumnsAfter(lastCol, colsToAdd);
                 Logger.log(`Inserted ${colsToAdd} column(s) after column ${lastCol} in sheet "${sheetName}".`);
             }

            // Now set the header in the newly ensured column UUID_COL_INDEX
            sheet.getRange(SOURCE_HEADER_ROW, UUID_COL_INDEX).setValue('UUID');
            Logger.log(`Set header "UUID" in new column ${UUID_COL_INDEX} in sheet "${sheetName}".`);
         }
         SpreadsheetApp.flush(); // Apply changes
       } catch (e) {
          Logger.log(`ERROR trying to add/update UUID column in sheet "${sheetName}": ${e.message}. Skipping sheet.`);
          notifyError_('ensureUuidColumnForAllMonths_', e, `Failed for sheet: ${sheetName}`);
          // Continue to next sheet instead of throwing to allow setup to partially succeed
       }
    }
  } // End loop
  Logger.log('UUID column check completed.');
}

/**
 * Ensures the log sheet exists in the master spreadsheet with the correct headers.
 * Creates the sheet if it doesn't exist.
 */
function ensureLogSheet_() {
  Logger.log(`Ensuring log sheet "${LOG_TAB_NAME}" exists in master (${MASTER_SPREADSHEET_ID})...`);
  const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
   if (!ss) {
      throw new Error(`Cannot ensure log sheet: Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);
  }
  let logSheet = ss.getSheetByName(LOG_TAB_NAME);

  // Added 'Skipped (Invalid Data)' column
  const expectedHeaders = [
    'Timestamp', 'Status', 'Rows Inspected', 'Skipped (No UUID)',
    'Skipped (Duplicate)', 'Skipped (Invalid Data)', 'Rows Added', 'Duration (s)', 'Error Message'
  ];

  if (!logSheet) {
    logSheet = ss.insertSheet(LOG_TAB_NAME);
    Logger.log(`Created log sheet "${LOG_TAB_NAME}".`);
    logSheet.appendRow(expectedHeaders);
    logSheet.setFrozenRows(1);
    logSheet.getRange(1, 1, 1, expectedHeaders.length).setFontWeight("bold");
    try {
        logSheet.setColumnWidths(1, expectedHeaders.length, 150); // Adjust width if needed
    } catch (e) {
        Logger.log(`Warning: Could not set column widths for log sheet "${LOG_TAB_NAME}". ${e.message}`);
    }
  } else {
    // Verify headers if sheet exists
    let headersOk = false;
    const lastLogCol = logSheet.getLastColumn();
    if (logSheet.getLastRow() >= 1 && lastLogCol > 0) {
        // Read only up to the number of expected headers or last column, whichever is smaller
        const colsToRead = Math.min(expectedHeaders.length, lastLogCol);
        const headerRange = logSheet.getRange(1, 1, 1, colsToRead);
        const currentHeaders = headerRange.getValues()[0];

        // Compare length first, then content if lengths match
        headersOk = currentHeaders.length === expectedHeaders.length &&
                    currentHeaders.every((val, index) => val === expectedHeaders[index]);
    }

    if (!headersOk) {
      Logger.log(`Log sheet "${LOG_TAB_NAME}" found, but headers are missing or incorrect. Resetting headers.`);
      // Ensure enough columns exist
      if(logSheet.getMaxColumns() < expectedHeaders.length) {
        logSheet.insertColumnsAfter(logSheet.getMaxColumns(), expectedHeaders.length - logSheet.getMaxColumns());
      }
      // Clear existing header range and set new ones
      logSheet.getRange(1, 1, 1, logSheet.getMaxColumns()).clearContent();
      logSheet.getRange(1, 1, 1, expectedHeaders.length).setValues([expectedHeaders]).setFontWeight("bold");
      logSheet.setFrozenRows(1);
      try {
        logSheet.setColumnWidths(1, expectedHeaders.length, 150);
      } catch (e) {
        Logger.log(`Warning: Could not set column widths for log sheet "${LOG_TAB_NAME}" after resetting headers. ${e.message}`);
      }
      // Optional: Clear old data below incorrect headers?
      // if (logSheet.getLastRow() > 1) {
      //    logSheet.getRange(2, 1, logSheet.getLastRow()-1, expectedHeaders.length).clearContent();
      // }
    }
  }
  Logger.log(`Log sheet "${LOG_TAB_NAME}" is ready.`);
}

/**
 * Ensures the master "Data" tab has the correct header row.
 * Overwrites row 1 with the expected column names defined within the function.
 */
function ensureMasterHeaders_() {
  Logger.log(`Ensuring headers in master sheet tab: "${MASTER_TAB_NAME}"...`);
  const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
  const sh = ss.getSheetByName(MASTER_TAB_NAME);
  if (!sh) {
    throw new Error(`ensureMasterHeaders_: Tab "${MASTER_TAB_NAME}" not found in master sheet (${MASTER_SPREADSHEET_ID}).`);
  }
  // Define the canonical master headers
  const expected = [
    'date', 'hours_worked', 'location', 'provider_name', 'provider_type',
    'source_sheet', 'production_goal_daily', 'verified_production', 'bonus',
    'humble_production', 'baytown_production', 'monthly_goal', 'external_id', 'uuid'
  ];

  const lastCol = sh.getLastColumn();
  let currentHeaders = [];
  if (sh.getLastRow() >= 1 && lastCol > 0) {
      currentHeaders = sh.getRange(1, 1, 1, lastCol).getValues()[0];
  }

  // Check if headers match exactly (order and content)
  const headersMatch = currentHeaders.length === expected.length &&
                       currentHeaders.every((h, i) => h === expected[i]);

  if (!headersMatch) {
      Logger.log('Master headers are incorrect or missing. Resetting headers...');
      // Ensure enough columns exist
      if (lastCol < expected.length) {
        sh.insertColumnsAfter(lastCol, expected.length - lastCol);
      } else if (lastCol > expected.length) {
        // Optionally delete extra columns, or just overwrite
        // sh.deleteColumns(expected.length + 1, lastCol - expected.length);
      }
      // Overwrite the first row
      sh.getRange(1, 1, 1, expected.length).setValues([expected]);
      sh.setFrozenRows(1); // Re-apply frozen row
      sh.getRange(1, 1, 1, expected.length).setFontWeight("bold"); // Re-apply bold
      Logger.log('Master headers reset.');
  } else {
      Logger.log('Master headers are correct.');
  }
}