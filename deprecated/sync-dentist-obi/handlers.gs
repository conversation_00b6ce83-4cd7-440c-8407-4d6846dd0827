/**
 * @fileoverview Event handlers module for the OBI Dentist Sync application.
 * 
 * This module contains handlers for various events and user interactions, including:
 * - Spreadsheet edit event handlers
 * - Form submission handlers
 * - UI interaction handlers
 * - Scheduled task completion handlers
 * - Error and exception handlers
 * - Webhook and callback handlers
 * 
 * The handlers module provides the connection between user actions or
 * system events and the application's business logic, ensuring appropriate
 * responses to different types of events while maintaining separation of
 * concerns.
 */

/**
 * Handles the onEdit event for the provider's source spreadsheet.
 * Adds a UUID to the corresponding row (in UUID_COL_INDEX) if a relevant production
 * column (e.g., HUMBLE_PROD_COL_INDEX, BAYTOWN_PROD_COL_INDEX) is edited with a valid number,
 * the date (in DATE_COL_INDEX) is valid and not in the future, and no UUID currently exists.
 * Notifies via email on error.
 *
 * @param {GoogleAppsScript.Events.SheetsOnEdit} e The event object.
 */
function onEditHandler(e) {
  let sheetName = 'Unknown'; // Default value
  let editedRow = 0; // Initialize as number
  try {
    // Validate the event object and range
    if (!e || !e.range) return; // Exit if event is invalid

    const sheet = e.range.getSheet();
    sheetName = sheet.getName();
    const editedCol = e.range.getColumn();
    editedRow = e.range.getRow();

    // Check if the edit is in a valid monthly sheet, a production column, and data row
    if (!isMonthlySheet_(sheetName)) return;
    if (editedCol !== HUMBLE_PROD_COL_INDEX && editedCol !== BAYTOWN_PROD_COL_INDEX) return;
    if (editedRow < SOURCE_DATA_START_ROW) return;

    // Check if UUID cell is already filled
    const uuidCell = sheet.getRange(editedRow, UUID_COL_INDEX);
    if (uuidCell.getValue()) return; // Don't overwrite existing UUID

    // --- Check Date Validity and Production Data ---
    const dateCell = sheet.getRange(editedRow, DATE_COL_INDEX);
    const dateValue = dateCell.getValue();
    const humbleProdValue = sheet.getRange(editedRow, HUMBLE_PROD_COL_INDEX).getValue();
    const baytownProdValue = sheet.getRange(editedRow, BAYTOWN_PROD_COL_INDEX).getValue();

    // 1. Validate Date
    if (!dateValue || !(dateValue instanceof Date) || Number.isNaN(dateValue.getTime())) {
      // Logger.log(`onEditHandler: Skipping UUID for row ${editedRow} in "${sheetName}": Invalid/blank date.`);
      return;
    }

    // 2. Check if Date is in the future
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today
    const sourceDate = new Date(dateValue);
    sourceDate.setHours(0, 0, 0, 0); // Normalize source date
    if (sourceDate > today) {
      // Logger.log(`onEditHandler: Skipping UUID for row ${editedRow} in "${sheetName}": Date is in the future.`);
    return;
  }
  
    // 3. Check if *at least one* production value is a valid number
    const hasValidProduction = (typeof humbleProdValue === 'number' && !Number.isNaN(humbleProdValue)) ||
                               (typeof baytownProdValue === 'number' && !Number.isNaN(baytownProdValue));

    if (!hasValidProduction) {
        // Logger.log(`onEditHandler: Skipping UUID for row ${editedRow} in "${sheetName}": No valid production data.`);
        return;
    }
    // --- End Checks ---


    // If all checks pass, add the UUID
    const newUuid = Utilities.getUuid();
    uuidCell.setValue(newUuid);
    // Logger.log(`onEditHandler: Added UUID ${newUuid} to ${sheetName}, row ${editedRow}`);

  } catch (err) {
    const errorMessage = `Error in onEditHandler for sheet "${sheetName}", row ${editedRow}: ${err.message}`;
    Logger.log(`${errorMessage}\n${err.stack}`);
    // Notify on errors during onEdit. Be mindful of potential email volume if errors are frequent.
    notifyError_('onEditHandler', err, `Sheet: ${sheetName}, Row: ${editedRow}`);
  }
}