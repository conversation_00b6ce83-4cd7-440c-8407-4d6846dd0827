/**
 * @fileoverview Utility functions module for the KAM Dentist Sync application.
 * 
 * This module provides general-purpose utility functions used throughout
 * the application, including:
 * - Date and time manipulation
 * - String formatting and parsing
 * - Array and object manipulation
 * - Spreadsheet helper functions
 * - Caching and memoization utilities
 * - Rate limiting and throttling mechanisms
 * - Common validation routines
 * 
 * The utils module centralizes commonly used functions to promote code
 * reuse, consistency, and maintainability across the application.
 */

/**
 * Safely assigns a value to the correct position in the output row array based on the header name.
 * Finds the index of the header name (case-insensitive) and places the value there.
 * Converts null or undefined values to empty strings.
 * If the header name is not found, it logs a warning but does not throw an error.
 *
 * @param {any[]} outputRow The array representing the row being built.
 * @param {string[]} headers The array of header names from the master sheet.
 * @param {string} headerName The name of the header column to assign the value to.
 * @param {*} value The value to assign.
 */
function assignValue_(outputRow, headers, headerName, value) {
  const lowerCaseHeaderName = headerName.toLowerCase();
  const index = headers.findIndex(h => h.toString().toLowerCase() === lowerCaseHeaderName);
  if (index !== -1) {
    // Store null/undefined as blanks, otherwise use the value
    outputRow[index] = (value === null || value === undefined) ? '' : value;
  } else {
    Logger.log(`Warning: Header "${headerName}" not found in master sheet headers during row construction.`);
  }
}

/**
 * Checks if a sheet name matches the expected monthly format (e.g., "Apr-25", "May 2024").
 * Uses a standard regex (3-letter month, hyphen, 2-digit year), adjust if needed.
 *
 * @param {string} name The sheet name to test.
 * @returns {boolean} True if the name matches the pattern, false otherwise.
 */
function isMonthlySheet_(name) {
  if (!name || typeof name !== 'string') return false;
  // Regex: 3 letters (case-insensitive), hyphen, 2 digits. Anchored.
  const monthlySheetRegex = /^[A-Za-z]{3}-\d{2}$/;
  // Alternative allowing space and 4-digit year: /^[A-Za-z]{3,}[-\s]?\d{2,4}$/i;
  return monthlySheetRegex.test(name.trim());
}

/**
 * Lists all triggers currently associated with this script project.
 * Useful for debugging trigger issues.
 */
function listAllTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  if (triggers.length === 0) {
      Logger.log('No triggers found for this project.');
      return;
  }
  Logger.log(`Found ${triggers.length} trigger(s):`);
  for (const t of triggers) {
    Logger.log(
      `- Handler: ${t.getHandlerFunction()}, Type: ${t.getEventType()}, Source: ${t.getTriggerSource()}, ID: ${t.getUniqueId()}`
    );
  }
}