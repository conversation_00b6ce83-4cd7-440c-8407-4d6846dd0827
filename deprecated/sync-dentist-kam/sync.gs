/**
 * @fileoverview Core synchronization module for the KAM Dentist Sync application.
 * 
 * This module contains the primary synchronization logic, including:
 * - Data fetching from external sources (APIs, databases, etc.)
 * - Comparison of local and remote data to identify changes
 * - Conflict detection and resolution strategies
 * - Batch processing and chunking for large datasets
 * - Error handling and recovery during sync operations
 * - Progress tracking and reporting
 * 
 * The sync module is responsible for ensuring data consistency between
 * the Google Sheet and external systems, handling the complexities of
 * bidirectional data synchronization.
 */

/**
 * Synchronizes the provider's data from source monthly sheets to the master sheet.
 * Identifies new rows based on UUIDs and appends them to the master data tab.
 * Logs the execution details to the log tab.
 */
function syncToExternalMaster() {
  const functionName = SYNC_FUNCTION_NAME;
  const runStart = new Date();
  let status = 'Success';
  let errorMessage = '';
  let inspected = 0;
  let skippedNoUuid = 0;
  let skippedDup = 0;
  let skippedInvalid = 0; // Counter for rows skipped due to invalid date/prod
  let added = 0;

  try {
    Logger.log(`Starting sync for ${PROVIDER_NAME}...`);
    Logger.log(`Master URL → ${SpreadsheetApp.openById(MASTER_SPREADSHEET_ID).getUrl()}`);

    // --- Open Spreadsheets ---
    const sourceSpreadsheet = SpreadsheetApp.openById(SOURCE_SHEET_ID);
    if (!sourceSpreadsheet) throw new Error(`Source Spreadsheet not found or inaccessible (ID: ${SOURCE_SHEET_ID})`);

    const masterSpreadsheet = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
    if (!masterSpreadsheet) throw new Error(`Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);

    const masterSheet = masterSpreadsheet.getSheetByName(MASTER_TAB_NAME);
    if (!masterSheet) throw new Error(`Master tab "${MASTER_TAB_NAME}" not found in Master Sheet (ID: ${MASTER_SHEET_ID})`);

    // --- Get Master Sheet Headers and UUID Column Index ---
    const masterLastCol = masterSheet.getLastColumn();
    if (masterLastCol === 0 || masterSheet.getLastRow() === 0) {
       throw new Error(`Master tab "${MASTER_TAB_NAME}" appears to be empty or header row is missing.`);
    }
    const masterHeaders = masterSheet.getRange(1, 1, 1, masterLastCol).getValues()[0];
    const uuidMasterColIndex = masterHeaders.findIndex(h => h.toString().toLowerCase() === 'uuid'); // 0-based index

    if (uuidMasterColIndex === -1) throw new Error('Column "uuid" not found in master header row 1.');

    // --- Build Set of Existing UUIDs from Master Sheet ---
    const masterLastRow = masterSheet.getLastRow();
    const existingUuids = new Set();
    if (masterLastRow > 1) {
      const uuidValues = masterSheet.getRange(2, uuidMasterColIndex + 1, masterLastRow - 1, 1)
                                  .getValues()
                                  .flat()
                                  .filter(String);
      for (const uuid of uuidValues) {
          existingUuids.add(uuid);
      }
    }
    Logger.log(`Found ${existingUuids.size} existing UUIDs in master sheet.`);

    // --- Process Source Sheets ---
    const rowsToAppendBatch = [];
    const sourceSheets = sourceSpreadsheet.getSheets();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (const sheet of sourceSheets) {
      const sheetName = sheet.getName();
      if (!isMonthlySheet_(sheetName)) continue;

      Logger.log(`Processing sheet: "${sheetName}"`);
      const lastRow = sheet.getLastRow();
      if (lastRow < SOURCE_DATA_START_ROW) {
        Logger.log(`Skipping sheet "${sheetName}" - no data found starting from row ${SOURCE_DATA_START_ROW}.`);
        continue;
      }
      
      // Read all relevant data from the current sheet
      const lastColSource = sheet.getLastColumn();
      // Ensure we read up to the highest index needed (UUID_COL_INDEX)
      const readLastCol = Math.max(lastColSource, UUID_COL_INDEX, DATE_COL_INDEX, HUMBLE_PROD_COL_INDEX, BAYTOWN_PROD_COL_INDEX, MONTHLY_GOAL_COL_INDEX);
      const numRowsToRead = lastRow - SOURCE_DATA_START_ROW + 1;
      if (numRowsToRead <= 0) continue; // Should be caught by lastRow check, but safety first

      const sheetData = sheet.getRange(SOURCE_DATA_START_ROW, 1, numRowsToRead, readLastCol).getValues();

      for (const [index, row] of sheetData.entries()) {
        const sourceRowNum = SOURCE_DATA_START_ROW + index;
        inspected++;

        const uuid = row[UUID_COL_INDEX - 1]; // 0-based index
        const dateValue = row[DATE_COL_INDEX - 1];
        const humbleProd = row[HUMBLE_PROD_COL_INDEX - 1];
        const baytownProd = row[BAYTOWN_PROD_COL_INDEX - 1];

        // --- VALIDATION CHECKS ---
        // 1. Check Date validity
        if (!dateValue || !(dateValue instanceof Date) || Number.isNaN(dateValue.getTime())) {
            // Logger.log(`Sync Skipping row ${sourceRowNum} in "${sheetName}": Invalid/blank date.`);
            skippedInvalid++;
            continue;
        }

        // 2. Check Date is not in the future
        const sourceDate = new Date(dateValue);
        sourceDate.setHours(0, 0, 0, 0); // Normalize
        if (sourceDate > today) {
            // Logger.log(`Sync Skipping row ${sourceRowNum} in "${sheetName}": Date is in the future.`);
            skippedInvalid++;
            continue;
        }

        // 3. Check for *at least one* valid production value
        const hasValidProduction = (typeof humbleProd === 'number' && !Number.isNaN(humbleProd)) ||
                                   (typeof baytownProd === 'number' && !Number.isNaN(baytownProd));
        if (!hasValidProduction) {
           // Logger.log(`Sync Skipping row ${sourceRowNum} in "${sheetName}": No valid production data.`);
           skippedInvalid++;
           continue;
        }
        // --- END VALIDATION CHECKS ---


        // --- UUID Checks ---
        if (!uuid) {
          skippedNoUuid++;
          continue;
        }
        if (existingUuids.has(uuid)) {
          skippedDup++;
          continue;
        }
        // --- End UUID Checks ---


        // --- Build Row for Master Sheet ---
        try {
           const masterRow = buildMasterRow_(masterHeaders, row, sheetName);
           rowsToAppendBatch.push(masterRow);
           existingUuids.add(uuid); // Add to set to prevent duplicates within this run
           added++;
        } catch (buildErr) {
           Logger.log(`Error building master row from source sheet "${sheetName}", row ${sourceRowNum}: ${buildErr.message}`);
           // Consider adding to a 'failed rows' count or similar if needed
        }
      } // End inner loop (rows)
    } // End outer loop (sheets)

    // --- Write to Master Sheet ---
    if (rowsToAppendBatch.length > 0) {
      const targetRowNum = masterSheet.getLastRow() + 1;
      const numRowsToAppend = rowsToAppendBatch.length;
      const numColsToAppend = masterHeaders.length;

      if (numColsToAppend > 0) {
        Logger.log(`Attempting to write ${numRowsToAppend} new rows to master sheet.`);
        const targetRange = masterSheet.getRange(targetRowNum, 1, numRowsToAppend, numColsToAppend);
        targetRange.setValues(rowsToAppendBatch);
        Logger.log(`Successfully appended ${numRowsToAppend} rows.`);
      } else {
          throw new Error("Master header length is zero. Cannot determine number of columns to append.");
      }
    } else {
      Logger.log('No new valid rows found to append.');
    }

  } catch (err) {
    Logger.log(`ERROR in ${functionName}: ${err.message}\n${err.stack}`);
    status = 'Failure';
    errorMessage = err.message;
    notifyError_(functionName, err);
  } finally {
    const runEnd = new Date();
    const durationSeconds = (runEnd.getTime() - runStart.getTime()) / 1000;
    // Log with the new skippedInvalid count
    logRun_(runStart, status, inspected, skippedNoUuid, skippedDup, skippedInvalid, added, durationSeconds, errorMessage);
    Logger.log(`${functionName} finished. Status: ${status}. Duration: ${durationSeconds.toFixed(2)}s. Added: ${added}. Skipped (No UUID/Dup/Invalid): ${skippedNoUuid}/${skippedDup}/${skippedInvalid}.`);
  }
}