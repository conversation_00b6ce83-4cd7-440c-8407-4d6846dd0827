/**
 * @fileoverview Logging module for the KAM Dentist Sync application.
 * 
 * This module provides comprehensive logging capabilities, including:
 * - Structured logging with consistent formatting
 * - Multiple log levels (debug, info, warning, error)
 * - Log persistence to spreadsheet and other destinations
 * - Performance and execution metrics
 * - Audit trail for critical operations
 * - Log rotation and management
 * 
 * The logging module ensures that all application activities are properly
 * recorded for monitoring, debugging, and compliance purposes.
 */

/**
 * Logs a summary of a sync run to the designated log sheet.
 * Includes the new 'Skipped (Invalid Data)' count.
 *
 * @param {Date} startTime The Date object marking the start of the run.
 * @param {string} status The final status ('Success' or 'Failure').
 * @param {number} inspected Total rows checked in source sheets.
 * @param {number} noUuid Rows skipped due to missing UUID.
 * @param {number} duplicate Rows skipped because UUID already exists in master.
 * @param {number} skippedInvalid Rows skipped due to invalid date or lack of production data.
 * @param {number} added Rows successfully added to the master sheet.
 * @param {number} durationSeconds The duration of the run in seconds.
 * @param {string} [errorMessage=''] Optional error message if status is 'Failure'.
 */
function logRun_(startTime, status, inspected, noUuid, duplicate, skippedInvalid, added, durationSeconds, errorMessage = '') {
  try {
    const ss = SpreadsheetApp.openById(MASTER_SPREADSHEET_ID);
     if (!ss) {
        Logger.log(`ERROR: Cannot log run - Master Spreadsheet not found or inaccessible (ID: ${MASTER_SPREADSHEET_ID})`);
        return;
    }
    const logSheet = ss.getSheetByName(LOG_TAB_NAME);
    if (!logSheet) {
      Logger.log(`ERROR: Log sheet "${LOG_TAB_NAME}" not found. Cannot log run.`);
      // Attempt to recreate if missing? Risky if ID is wrong.
      // try { ensureLogSheet_(); logSheet = ss.getSheetByName(LOG_TAB_NAME); } catch(e) { /* ignore */ }
      if (!logSheet) return; // Give up if still missing
    }

    // Append the log entry including the new skippedInvalid count
    logSheet.appendRow([
      startTime,
      status,
      inspected,
      noUuid,
      duplicate,
      skippedInvalid, // New column value
      added,
      durationSeconds.toFixed(2),
      errorMessage
    ]);
  } catch (logErr) {
    Logger.log(`CRITICAL ERROR: Failed to write to log sheet "${LOG_TAB_NAME}". Status: ${status}, Added: ${added}. Error: ${logErr.message}\n${logErr.stack}`);
    notifyError_('logRun_', logErr, `Failed to log run results. Status: ${status}.`);
  }
}