/**
 * @fileoverview Configuration module for the KAM Dentist Sync application.
 * 
 * This module contains all configuration constants and settings used throughout
 * the application, including:
 * - API endpoints and credentials
 * - Spreadsheet IDs and sheet names
 * - Column mappings and data structure definitions
 * - Timing and retry settings
 * - Feature flags and operational modes
 * 
 * Centralizing configuration in this module makes it easier to maintain and
 * update settings without modifying business logic code.
 */

// ────────────────────── CONFIGURATION ──────────────────────

/** @const {string} ID of the source Spreadsheet containing provider's monthly data tabs. */
const SOURCE_SHEET_ID = "14aZwhhacS7nW5K2r7Bp4lJKSaJ2__cen1vtJKqqIa78"; // Dr. <PERSON><PERSON>'s source spreadsheet

/** @const {string} ID of the master Spreadsheet where data will be consolidated. */
const MASTER_SPREADSHEET_ID = "1Jht7tr8Hh7jHWi-NM9JP7vMvsg69RBQaM4E5hA7p_ko";

/** @const {string} Name of the tab within the master Spreadsheet to write data to. */
const MASTER_TAB_NAME = "Data";

/** @const {string} Name of the tab within the master Spreadsheet for execution logs. */
const LOG_TAB_NAME = 'Sync-Logs';

// --- Provider Details ---
/** @const {string} Name of the provider this script instance handles. */
const PROVIDER_NAME = "Dr. Kam";
/** @const {string} Type of the provider. */
const PROVIDER_TYPE = "Dentist";
// NOTE: Provider data may vary in structure.
// Adapt column indices and logic as needed.

// --- Source Sheet Structure ---
/** @const {number} Row number containing headers in the source monthly sheets (adjust if needed). */
const SOURCE_HEADER_ROW = 1; // Assuming header is row 1
/** @const {number} Row number where data starts in the source monthly sheets. */
const SOURCE_DATA_START_ROW = 2;

// --- Column Indices (1-based) for THIS Provider's Source Sheet ---
/** @const {number} 1-based index for the Date column. */
const DATE_COL_INDEX = 1; // Column A
/** @const {number} 1-based index for the Humble Production column. */
const HUMBLE_PROD_COL_INDEX = 3; // Column C
/** @const {number} 1-based index for the Baytown Production column. */
const BAYTOWN_PROD_COL_INDEX = 4; // Column D
/** @const {number} 1-based index for the Monthly Goal column. */
const MONTHLY_GOAL_COL_INDEX = 10; // Column J
/** @const {number} 1-based index for the UUID column in source sheets. */
const UUID_COL_INDEX = 15; // Column O <<< User specified

// --- Core Function Names (for triggers, menus, etc.) ---
/** @const {string} Name of the main synchronization function. */
const SYNC_FUNCTION_NAME = 'syncToExternalMaster';
/** @const {string} Name of the function handling onEdit events. */
const EDIT_HANDLER_FUNCTION_NAME = 'onEditHandler';
/** @const {string} Name of the UUID seeding function. */
const SEED_UUIDS_FUNCTION_NAME = 'seedAllMissingUuids_';
/** @const {string} Name of the setup function. */
const SETUP_FUNCTION_NAME = 'Setup';