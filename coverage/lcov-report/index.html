
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">97.88% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>833/851</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">79.86% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>234/293</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.14% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>69/84</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">97.88% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>833/851</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="config"><a href="config/index.html">config</a></td>
	<td data-value="89.33" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 89%"></div><div class="cover-empty" style="width: 11%"></div></div>
	</td>
	<td data-value="89.33" class="pct high">89.33%</td>
	<td data-value="75" class="abs high">67/75</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="5" class="abs medium">3/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="89.33" class="pct high">89.33%</td>
	<td data-value="75" class="abs high">67/75</td>
	</tr>

<tr>
	<td class="file high" data-value="services"><a href="services/index.html">services</a></td>
	<td data-value="97.16" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.16" class="pct high">97.16%</td>
	<td data-value="247" class="abs high">240/247</td>
	<td data-value="67.5" class="pct medium">67.5%</td>
	<td data-value="40" class="abs medium">27/40</td>
	<td data-value="93.75" class="pct high">93.75%</td>
	<td data-value="16" class="abs high">15/16</td>
	<td data-value="97.16" class="pct high">97.16%</td>
	<td data-value="247" class="abs high">240/247</td>
	</tr>

<tr>
	<td class="file high" data-value="utils"><a href="utils/index.html">utils</a></td>
	<td data-value="99.43" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 99%"></div><div class="cover-empty" style="width: 1%"></div></div>
	</td>
	<td data-value="99.43" class="pct high">99.43%</td>
	<td data-value="529" class="abs high">526/529</td>
	<td data-value="82.25" class="pct high">82.25%</td>
	<td data-value="248" class="abs high">204/248</td>
	<td data-value="79.1" class="pct medium">79.1%</td>
	<td data-value="67" class="abs medium">53/67</td>
	<td data-value="99.43" class="pct high">99.43%</td>
	<td data-value="529" class="abs high">526/529</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-23T14:16:11.309Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    