
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for recalls/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> recalls/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.92% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>317/529</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">60% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>27/45</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">43.33% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>13/30</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.92% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>317/529</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="errorHandler.js"><a href="errorHandler.js.html">errorHandler.js</a></td>
	<td data-value="59.27" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 59%"></div><div class="cover-empty" style="width: 41%"></div></div>
	</td>
	<td data-value="59.27" class="pct medium">59.27%</td>
	<td data-value="194" class="abs medium">115/194</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="13" class="abs medium">10/13</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="8" class="abs low">2/8</td>
	<td data-value="59.27" class="pct medium">59.27%</td>
	<td data-value="194" class="abs medium">115/194</td>
	</tr>

<tr>
	<td class="file medium" data-value="sharedUtils.js"><a href="sharedUtils.js.html">sharedUtils.js</a></td>
	<td data-value="64.13" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 64%"></div><div class="cover-empty" style="width: 36%"></div></div>
	</td>
	<td data-value="64.13" class="pct medium">64.13%</td>
	<td data-value="184" class="abs medium">118/184</td>
	<td data-value="53.33" class="pct medium">53.33%</td>
	<td data-value="30" class="abs medium">16/30</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="16" class="abs medium">10/16</td>
	<td data-value="64.13" class="pct medium">64.13%</td>
	<td data-value="184" class="abs medium">118/184</td>
	</tr>

<tr>
	<td class="file medium" data-value="validator.js"><a href="validator.js.html">validator.js</a></td>
	<td data-value="55.62" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 55%"></div><div class="cover-empty" style="width: 45%"></div></div>
	</td>
	<td data-value="55.62" class="pct medium">55.62%</td>
	<td data-value="151" class="abs medium">84/151</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="6" class="abs low">1/6</td>
	<td data-value="55.62" class="pct medium">55.62%</td>
	<td data-value="151" class="abs medium">84/151</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-23T13:39:30.843Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    