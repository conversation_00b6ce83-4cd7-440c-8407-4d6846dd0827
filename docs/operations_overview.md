# Operations Scripts Overview

## Current Structure

```
operations/
├── recalls/
│   ├── humble/
│   │   └── Recall.js
│   ├── consults/
│   └── baytown/
├── metrics/
│   ├── front-desk-eod/
│   │   └── main.gs
│   └── data-syncs/
├── labcases/
│   ├── humble/
│   └── baytown/
└── billing/
    └── humble/
```

## Script Analysis

### 1. Recalls System
**Location**: `operations/recalls/{location}/`
**Purpose**: Manages patient recall tracking and status updates
**Key Features**:
- Status-based row movement between sheets
- Daily recall logging
- Backlog processing
- Custom menu interface

**Current Implementation**:
- Single file containing all logic
- Basic configuration object
- Status mapping constant
- Event handlers (onEdit, onOpen)
- Utility functions

**Areas for Improvement**:
- Separate configuration into dedicated file
- Create service layer for sheet operations
- Implement proper error handling module
- Add input validation layer
- Create shared utilities across locations

### 2. Front Desk EOD Metrics
**Location**: `operations/metrics/front-desk-eod/`
**Purpose**: Manages end-of-day metrics calculations and formatting
**Key Features**:
- Formula application across sheets
- Percentage formatting
- Format copying between locations
- Error handling

**Current Implementation**:
- Single main.gs file
- Direct sheet manipulation
- Hardcoded column references
- Basic error handling

**Areas for Improvement**:
- Extract configuration (column indices, formulas)
- Create sheet service layer
- Implement proper validation
- Add logging system
- Create reusable formatting utilities

## Modularization Plan

### Phase 1: Core Infrastructure Setup

1. Create Base Directory Structure
```
operations/
├── shared/
│   ├── config/
│   │   ├── sheets.config.js
│   │   └── formulas.config.js
│   ├── services/
│   │   ├── sheet.service.js
│   │   └── logging.service.js
│   └── utils/
│       ├── date.utils.js
│       └── validation.utils.js
```

2. Implement Shared Services
- SheetService: Common sheet operations
- LoggingService: Standardized logging
- ValidationService: Input checking

### Phase 2: Recalls System Modularization

1. New Structure for Each Location
```
operations/recalls/{location}/
├── config/
│   ├── sheets.config.js
│   └── status.config.js
├── services/
│   ├── recall.service.js
│   └── log.service.js
├── handlers/
│   ├── edit.handler.js
│   └── menu.handler.js
├── utils/
│   └── date.utils.js
└── main.js
```

2. Implementation Steps:
a) Extract Configuration
```javascript
// sheets.config.js
export const SHEET_CONFIG = {
  recallSheetName: "RECALL",
  logSheetName: "Log"
};

// status.config.js
export const STATUS_SHEET_MAP = {
  "scheduled": "SUCCESSFUL",
  "dnc": "DNC",
  // ...
};
```

b) Create Services
```javascript
// recall.service.js
export class RecallService {
  moveRowToSheet(sourceSheet, rowIndex, targetSheetName) {
    // Implementation
  }
  
  processExistingStatuses() {
    // Implementation
  }
}

// log.service.js
export class LogService {
  logDailyRecalls() {
    // Implementation
  }
  
  processBacklog() {
    // Implementation
  }
}
```

c) Create Handlers
```javascript
// edit.handler.js
export function handleEdit(e) {
  // Implementation using services
}

// menu.handler.js
export function createMenu() {
  // Implementation
}
```

### Phase 3: EOD Metrics Modularization

1. New Structure
```
operations/metrics/front-desk-eod/
├── config/
│   ├── columns.config.js
│   └── formulas.config.js
├── services/
│   ├── formula.service.js
│   └── format.service.js
├── utils/
│   └── sheet.utils.js
└── main.js
```

2. Implementation Steps:
a) Extract Configuration
```javascript
// columns.config.js
export const COLUMNS = {
  PERCENTAGE_COLS: {
    start: 19,
    end: 23
  }
};

// formulas.config.js
export const FORMULAS = {
  COLUMN_S: 'IF(E{row}=0, 0, (E{row}-F{row})/E{row})',
  // ...
};
```

b) Create Services
```javascript
// formula.service.js
export class FormulaService {
  applyFormulasToSheet(sheet, startRow) {
    // Implementation
  }
}

// format.service.js
export class FormatService {
  applyPercentageFormatting(sheet, startRow) {
    // Implementation
  }
}
```

### Phase 4: Testing & Documentation

1. Add Test Structure
```
operations/
├── tests/
│   ├── recalls/
│   └── metrics/
└── docs/
    ├── recalls/
    └── metrics/
```

2. Implementation Steps:
- Create test cases for each service
- Document public interfaces
- Add JSDoc comments
- Create usage examples

### Phase 5: Integration & Deployment

1. Update Deployment Process
- Implement proper versioning
- Add change logging
- Create deployment documentation

2. Integration Testing
- Test cross-module functionality
- Verify error handling
- Check performance impact

## Development Tools & Requirements

### Required Tools
1. **Google Cloud CLI**
   - Used for Google Apps Script project management
   - Enables local development and version control
   - Installation: [Google Cloud CLI Documentation](https://cloud.google.com/sdk/docs/install)
   - Key commands:
     ```bash
     gcloud auth login
     gcloud projects list
     gcloud config set project [PROJECT_ID]
     ```

2. **clasp (Command Line Apps Script Projects)**
   - Google's official CLI for Apps Script development
   - Installation: `npm install -g @google/clasp`
   - Key features:
     - Local development of Apps Script projects
     - Version control integration
     - Push/pull changes to/from Google Drive
   - Essential commands:
     ```bash
     clasp login
     clasp clone [SCRIPT_ID]
     clasp push
     clasp pull
     clasp versions
     ```

3. **Node.js & npm**
   - Required for clasp and other development tools
   - Recommended version: Latest LTS
   - Used for:
     - Package management
     - Development scripts
     - Testing framework

4. **Git**
   - Version control system
   - Branch management for feature development
   - Collaboration and code review

### Development Environment Setup

1. **VS Code Extensions**
   - Google Apps Script
   - JavaScript and TypeScript
   - Biome (Modern formatter and linter)

2. **Configuration Files**
   - `.claspignore`: Exclude files from clasp push
   - `biome.json`: Unified formatting and linting rules
   - `jsconfig.json`: JavaScript project settings
   - Example configurations provided in shared utils
   ```json
   // biome.json example
   {
     "$schema": "./node_modules/@biomejs/biome/configuration_schema.json",
     "formatter": {
       "enabled": true,
       "indentStyle": "space",
       "indentWidth": 2,
       "lineWidth": 80
     },
     "linter": {
       "enabled": true,
       "rules": {
         "recommended": true
       }
     },
     "javascript": {
       "formatter": {
         "quoteStyle": "single",
         "trailingComma": "es5"
       }
     }
   }
   ```

3. **Environment Variables**
   - Store sensitive information
   - Manage different environments (dev/prod)
   - Example `.env` structure:
     ```
     SCRIPT_ID_PROD=1234...
     SCRIPT_ID_DEV=5678...
     ```

### Best Practices

1. **Local Development**
   ```bash
   # Initial setup
   clasp clone "script_id"
   cd project_folder
   npm init
   
   # Development workflow
   clasp pull  # Get latest
   # Make changes
   clasp push  # Deploy changes
   clasp version  # Create new version
   ```

2. **Version Control**
   ```bash
   # Branch management
   git checkout -b feature/new-module
   # Make changes
   git commit -m "feat: add new module"
   git push origin feature/new-module
   ```

3. **Testing**
   - Use `jest` for unit testing
   - Mock Google Apps Script services
   - Run tests before deployment

4. **Deployment**
   ```bash
   # Production deployment
   clasp push
   clasp version 'Version description'
   clasp deploy [version] 'Deployment description'
   ```

## Migration Strategy

1. Incremental Implementation
- Start with shared infrastructure
- Migrate one module at a time
- Keep existing functionality working
- Add tests for new modules

2. Validation Steps
- Test each migrated component
- Verify existing functionality
- Document changes
- Update user guides

3. Rollback Plan
- Keep original scripts as backup
- Document reversion process
- Maintain version history

## Timeline Estimate

1. Phase 1: 1-2 days
- Setup directory structure
- Implement shared services

2. Phase 2: 2-3 days
- Modularize Recalls system
- Create and test services

3. Phase 3: 2-3 days
- Modularize EOD Metrics
- Implement new services

4. Phase 4: 1-2 days
- Add tests
- Create documentation

5. Phase 5: 1-2 days
- Integration testing
- Deployment preparation

Total Estimated Time: 7-12 days

## Next Steps

1. Review and approve modularization plan
2. Set up shared infrastructure
3. Begin with Recalls system migration
4. Implement automated testing
5. Document new structure and interfaces 