# UUID Flow in Production Data Sync System

## Overview

This document outlines the proper flow of UUIDs (Universally Unique Identifiers) in the production data sync system. UUIDs are critical for maintaining data integrity and preventing duplicate entries across the entire data pipeline.

## Correct UUID Flow

1. **Source Sheet Generates UUID**: When data is entered in a provider's source sheet, a UUID is generated for each valid row.
2. **UUID Travels with Data**: The UUID travels with the data when it's transferred to the master sheet.
3. **Master Sheet Preserves UUID**: The master sheet preserves the UUID when syncing to Supabase.
4. **Supabase Uses UUID for Deduplication**: Supabase uses the UUID to prevent duplicates through a unique constraint.

## UUID Generation Rules

UUIDs should **ONLY** be generated in source sheets under the following conditions:

1. **During Initialization**: The `seedAllMissingUuids_` function adds UUIDs to all valid existing rows in source sheets.
2. **During Editing**: The `onEditHandler` function adds a UUID when a valid row is edited in a source sheet.
3. **Valid Data Required**: UUIDs are only added to rows with:
   - A valid date (not in the future)
   - At least one valid production value (number, including zero and negative values)

## Important: No UUID Generation in Master Sheet

The master sheet should **NEVER** generate UUIDs. This is to prevent data duplication and ensure proper data flow. If a row in the master sheet is missing a UUID:

1. Check if the row was properly transferred from a source sheet
2. Verify that the source sheet has generated a UUID for the row
3. Re-sync the data from the source sheet to the master sheet

## Validation During Sync

During the sync process from source sheets to the master sheet:

1. Rows without UUIDs are skipped and logged
2. Rows with duplicate UUIDs are skipped and logged
3. Only rows with valid UUIDs are transferred to the master sheet

## Troubleshooting

If rows aren't syncing properly:

1. **Missing UUID**: Check if the row in the source sheet has a UUID. If not, it may not meet the validation criteria.
2. **Duplicate UUID**: Check if the UUID already exists in the master sheet.
3. **Invalid Data**: Ensure the row has valid date and production values.

## Implementation Details

- UUIDs are generated using `Utilities.getUuid()` in Google Apps Script
- The UUID column is typically column O (index 15) for dentist sheets, but can be configured per provider
- The master sheet uses column M (index 13) for UUIDs
- Supabase has a unique constraint on the UUID column to prevent duplicates
