/** @type {import('jest').Config} */
const config = {
  // Indicates that the root of your source code is the current directory
  roots: ['<rootDir>'],
  // Jest transformations -- this adds support for TypeScript using babel-jest
  transform: {
    // Use a single pattern to transform both JS/TS files with babel-jest
    '^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@babel/preset-env', '@babel/preset-typescript'] }],
  },
  // Test spec file resolution pattern
  // Matches parent folder `__tests__` and filename
  // should contain `test` or `spec`.
  testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.[jt]sx?$',
  // Module file extensions for importing
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  // Support for ES Modules
  extensionsToTreatAsEsm: ['.ts', '.tsx', '.jsx'],
  // Since the project uses "type": "module" in package.json,
  // we need to tell <PERSON><PERSON> to use Node's ESM capabilities.
  // The test script already uses --experimental-vm-modules, which is good.
  // We might need to adjust transformIgnorePatterns if dependencies use ESM.
  transformIgnorePatterns: [
    // Default: '/node_modules/'
    // If dependencies need transformation (e.g., they are ESM), adjust this pattern.
    // Example: '/node_modules/(?!(some-esm-pkg|another-esm-pkg)/)'
  ],
  // Add any setup files if needed, e.g., for global mocks
  // setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  // Collect coverage information
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageProvider: 'v8', // or 'babel'
};

export default config;