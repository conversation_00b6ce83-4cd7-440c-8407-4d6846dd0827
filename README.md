<p align="center">
  <img src="./logo.svg" alt="Unified Dental Scripts Logo" width="200" height="200">
</p>

# README: Unified Dental Scripts

## Overview

Unified Dental Scripts is a collection of Google Apps Script files designed to synchronize dental provider production data from individual spreadsheets to a central master spreadsheet. This system supports multiple dentists and hygienists across different locations, ensuring accurate and automated data consolidation for performance tracking and business analytics.

### Key Features

- **Automated Synchronization**: Data is synced from provider spreadsheets to the master spreadsheet every 6 hours.
- **UUID Tracking**: Unique identifiers ensure data integrity and prevent duplicate entries.
- **Trigger-Based Updates**: On-edit triggers generate UUIDs for new data entries instantly.
- **Error Handling**: Robust logging and email notifications for quick issue resolution.
- **Provider-Specific Configurations**: Tailored setups for dentists and hygienists to accommodate different data structures.

## Project Structure

The project is organized into separate directories for each provider's sync script:

- `sync-dentist-1/`, `sync-dentist-2/`, `sync-dentist-3/` for dentists
- `sync-hygiene-1/`, `sync-hygiene-2/` for hygienists
- `cleanup/` for a utility script to optimize spreadsheet performance

Each provider directory contains:

- `.clasp.json`: Configuration for connecting to Google Apps Script project
- `appsscript.json`: Google Apps Script manifest file
- Main script file (e.g., `dentist1.gs`, `hygienist1.gs`)

## Setup Instructions

### Prerequisites

- Google Workspace account with access to Google Sheets and Google Apps Script
- Node.js and pnpm for local development (optional, for type checking and linting)
- Clasp CLI for deploying scripts locally (optional)

### Initial Setup

1. **Clone the Repository**: If using local development, clone the repository to your machine.
2. **Install Dependencies**: Run `pnpm install` to set up necessary tools like TypeScript and Biome.
3. **Configure Spreadsheet IDs**: Update the `SOURCE_SHEET_ID` in each provider script with the respective provider's spreadsheet ID.
4. **Deploy Scripts**: Use `pnpm deploy` to push scripts to Google Apps Script, or manually copy the code into the script editor for each provider's spreadsheet.
5. **Run Setup Function**: Execute the `Setup` function for each provider script to configure UUID columns, triggers, and logging sheets. This can be done via the custom menu in the spreadsheet or by running `pnpm run:setup`.

### Running Synchronization

- **Automatic Sync**: The system automatically syncs data every 6 hours via time-based triggers.
- **Manual Sync**: Use the custom menu `Sync Tools (<Provider ID>)` in the spreadsheet to run `Run Full Sync Now`.

## Configuration Requirements

### General Configuration

Each provider script requires specific constants to be set at the top of the script file. These constants define the source and master spreadsheet IDs, provider details, and column indices for data mapping.

```javascript
/** @const {string} ID of the source Spreadsheet containing provider's monthly data tabs. */
const SOURCE_SHEET_ID = "YOUR_SOURCE_SHEET_ID_HERE"; // Replace with your actual source spreadsheet ID

/** @const {string} ID of the master Spreadsheet where data will be consolidated. */
const MASTER_SHEET_ID = 'YOUR_MASTER_SHEET_ID_HERE'; // Replace with your actual master spreadsheet ID

/** @const {string} Name of the tab within the master Spreadsheet to write data to. */
const MASTER_TAB_NAME = 'Data';

/** @const {string} Name of the tab within the master Spreadsheet for execution logs. */
const LOG_TAB_NAME = 'Sync-Logs';
```

### Dentist Provider Configuration

Dentists typically have multiple location columns in their spreadsheets. The configuration must reflect this structure:

- **UUID Column Index**: Typically set to Column O (index 15)
- **Production Columns**: Multiple columns for different locations, mapped in the `buildMasterRow_` function
- **Example Configuration**:

  ```javascript
  /** @const {number} 1-based index for the UUID column in source sheets. */
  const UUID_COL_INDEX = 15;
  /** @const {number} 1-based index for the Location A production column in source sheets. */
  const LOCATION_A_PROD_COL_INDEX = 4;
  /** @const {number} 1-based index for the Location B production column in source sheets. */
  const LOCATION_B_PROD_COL_INDEX = 5;
  ```

- **Provider Details**:

  ```javascript
  /** @const {string} Name of the provider this script instance handles. */
  const PROVIDER_NAME = 'Provider1';
  /** @const {string} Type of the provider. */
  const PROVIDER_TYPE = 'Dentist';
  /** @const {string} Default location value to assign. */
  const DEFAULT_LOCATION = '';
  ```

### Hygienist Provider Configuration

Hygienists typically have a single location with verified production data. The configuration is adjusted accordingly:

- **UUID Column Index**: Typically set to Column R (index 18)
- **Production Column**: Single column for verified production
- **Example Configuration**:

  ```javascript
  /** @const {number} 1-based index for the UUID column in source sheets. */
  const UUID_COL_INDEX = 18; // Column R
  /** @const {number} 1-based index for the Verified Production column in source sheets. */
  const VER_PROD_COL_INDEX = 4; // Column D
  ```

- **Provider Details**:

  ```javascript
  /** @const {string} Name of the provider this script instance handles. */
  const PROVIDER_NAME = 'Provider1'; // Example provider name
  /** @const {string} Type of the provider. */
  const PROVIDER_TYPE = 'Hygienist';
  /** @const {string} Default location value to assign. */
  const DEFAULT_LOCATION = 'Location1'; // Default location for this provider
  ```

### Customization in `buildMasterRow_` Function

The `buildMasterRow_` function maps source data to the master spreadsheet's structure. It must be customized based on provider type to ensure correct data placement:

- **Dentist Mapping**: Includes multiple production columns

  ```javascript
  assignValue_(masterRowOutput, masterHeaders, 'location_a_production', sourceRowData[LOCATION_A_PROD_COL_INDEX - 1]);
  assignValue_(masterRowOutput, masterHeaders, 'location_b_production', sourceRowData[LOCATION_B_PROD_COL_INDEX - 1]);
  ```

- **Hygienist Mapping**: Focuses on verified production

  ```javascript
  assignValue_(masterRowOutput, masterHeaders, 'verified_production', sourceRowData[VER_PROD_COL_INDEX - 1]);
  ```

## Inline Code Documentation

Where necessary, inline comments have been added to the scripts to clarify complex logic or configuration dependencies. For example, in the `onEditHandler` function:

```javascript
// --- NEW: Check date before adding UUID ---
const dateCell = sheet.getRange(editedRow, 1); // Column A is assumed to be Date
const dateValue = dateCell.getValue();

if (dateValue === null || dateValue === '' || !(dateValue instanceof Date) || Number.isNaN(dateValue.getTime())) {
    // Logger.log(`onEditHandler: Skipping UUID generation for row ${editedRow} in "${sheetName}": Invalid or blank date.`);
    return; // Don't add UUID if date is invalid/blank
}
```

These comments ensure that future maintainers understand the purpose and logic behind critical sections of the code.

## Troubleshooting

### Common Issues

1. **Rows Not Syncing**:
   - **Check UUID**: Ensure the row has a UUID. Rows without UUIDs are skipped.
   - **Duplicate UUID**: Verify if the UUID already exists in the master sheet, which would cause the row to be skipped.
   - **Validation Criteria**: Confirm the row meets validation criteria (valid date not in the future, at least one production value).
2. **Trigger Issues**:
   - **List Triggers**: Run `listAllTriggers()` to check active triggers.
   - **Reinstall Triggers**: Use the `Setup` function to reinstall triggers if necessary.
3. **Execution Time Limits**:
   - **Large Datasets**: Be aware of Google Apps Script's 6-minute execution limit. Optimize data processing if needed.
4. **Phantom Rows**:
   - **Cleanup Utility**: Run the cleanup script to remove phantom rows that may affect performance.

### Logs and Notifications

- **Execution Logs**: Check the Apps Script execution logs (View -> Executions) for detailed run information.
- **Sync Logs**: Review the `Sync-Logs` tab in the master spreadsheet for sync operation summaries.
- **Error Notifications**: Ensure the fallback email in `notifyError_` is set to a monitored address for error alerts.

## Development and Maintenance

### Local Development Workflow

1. Make changes locally.
2. Run type checking with `pnpm type-check`.
3. Lint and format code using `pnpm lint` and `pnpm format`.
4. Deploy changes with `pnpm deploy`.
5. Test setup or sync with `pnpm run:setup` or `pnpm run:sync`.

### Adding New Providers

1. Duplicate an existing provider directory and script.
2. Update configuration constants (`SOURCE_SHEET_ID`, `PROVIDER_NAME`, `PROVIDER_TYPE`, `DEFAULT_LOCATION`, column indices).
3. Customize the `buildMasterRow_` function if the data structure differs.
4. Deploy and run the `Setup` function for the new provider.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For support or to report issues, contact the project maintainer at <<EMAIL>>.
