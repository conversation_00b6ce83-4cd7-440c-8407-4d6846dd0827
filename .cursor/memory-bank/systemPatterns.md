# System Patterns: Unified Dental Scripts

## System Architecture

The project follows a distributed script architecture where each provider has their own dedicated script deployment:

```mermaid
graph TD
    subgraph "Provider Spreadsheets"
        DS1[<PERSON><PERSON> <PERSON> Spreadsheet]
        DS2[Dr. <PERSON><PERSON> Spreadsheet]
        DS3[Dr. <PERSON><PERSON> Spreadsheet]
        HS1[<PERSON><PERSON> Spreadsheet]
        HS2[<PERSON><PERSON> Spreadsheet]
    end
    
    subgraph "Provider Scripts"
        CS1[<PERSON>]
        CS2[<PERSON><PERSON>]
        CS3[<PERSON><PERSON>]
        HS1S[<PERSON><PERSON>]
        HS2S[<PERSON><PERSON>t]
    end
    
    MS[Master Spreadsheet]
    CU[Cleanup Utility]
    
    DS1 --> CS1
    DS2 --> CS2
    DS3 --> CS3
    HS1 --> HS1S
    HS2 --> HS2S
    
    CS1 --> MS
    CS2 --> MS
    CS3 --> MS
    HS1S --> MS
    HS2S --> MS
    
    CU --> DS1
    CU --> DS2
    CU --> DS3
    CU --> HS1
    CU --> HS2
```

## Key Technical Decisions

1. **Independent Script Deployments**: Each provider has an independent script bound to their spreadsheet, rather than a centralized script, to:
   - Simplify permissions management
   - Allow for provider-specific customizations
   - Isolate failures to single providers
   - Facilitate incremental deployment and updates

2. **UUID-Based Synchronization**: Using UUIDs to track individual data rows to:
   - Prevent duplicate entries in the master sheet
   - Allow for non-destructive updates
   - Support data integrity across multiple sync operations

3. **Event-Driven Architecture**: Using Google Apps Script triggers to:
   - Automatically run synchronization on a time schedule
   - Detect and process edits to generate UUIDs
   - Minimize manual intervention

4. **Provider Type Abstraction**: Supporting different provider types (dentists, hygienists) with tailored implementations but consistent patterns.

## Design Patterns

1. **Configuration-Based Customization**: Each script contains a configuration section that defines:
   - Source and target spreadsheet IDs
   - Provider details (name, type, default location)
   - Column indices for relevant data
   - Function name constants

2. **Function Naming Conventions**: Standardized function names across all scripts to:
   - Ensure consistent trigger registration
   - Support script portability
   - Facilitate maintenance and updates

3. **Defensive Programming**: Robust error handling and validation to:
   - Validate data before processing
   - Catch and log exceptions
   - Notify administrators of issues
   - Prevent script failures from affecting spreadsheet functionality

4. **Utility Function Pattern**: Common functionality extracted into reusable utility functions:
   - Sheet detection and validation
   - UUID generation and tracking
   - Logging and notification
   - Trigger management

## Component Relationships

1. **Provider Scripts → Master Spreadsheet**: One-way data flow from provider spreadsheets to master spreadsheet.

2. **Cleanup Utility → Provider Spreadsheets**: Separate utility to clean up "phantom rows" in provider spreadsheets.

3. **Triggers → Provider Scripts**: Time-based and event-based triggers invoke script functions.

4. **Provider Scripts → Logging**: Scripts log operations and errors to both Apps Script logs and a dedicated log sheet in the master spreadsheet. 