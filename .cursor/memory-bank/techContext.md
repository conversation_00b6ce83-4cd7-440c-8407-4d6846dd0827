# Technical Context: Unified Dental Scripts

## Technologies Used

### Primary Technologies
- **Google Apps Script (GAS)**: JavaScript-based scripting platform for Google Workspace applications
- **Google Sheets API**: Used to interact with source and master spreadsheets
- **Google Apps Script Triggers**: Time-based and event-based triggers for automation

### Development Tools
- **TypeScript**: Used for type checking during development to enhance code quality
- **Clasp CLI**: Command-line tool for local development and deployment of Google Apps Script projects
- **Biome**: Code linting and formatting tool
- **PNPM**: Package manager for managing Node.js dependencies

## Development Setup

### Local Development Environment
1. **Development Dependencies**:
   - Node.js and npm/pnpm for package management
   - TypeScript for type checking
   - Biome for linting/formatting
   - @types/google-apps-script for TypeScript definitions

2. **Project Structure**:
   - Separate directories for each provider's sync script
   - Each directory contains:
     - `.clasp.json`: Configuration for connecting to Google Apps Script project
     - `appsscript.json`: Google Apps Script manifest file
     - Main script file (e.g., `chi.gs`, `adriane.gs`)

3. **Development Workflow**:
   ```
   1. Make local changes
   2. Run type checking (pnpm type-check)
   3. Lint and format code (pnpm lint, pnpm format)
   4. Deploy to Google Apps Script (pnpm deploy)
   5. Run setup or sync functions (pnpm run:setup, pnpm run:sync)
   ```

## Technical Constraints

1. **Execution Limits**:
   - Google Apps Script has execution time limits (6 minutes per execution)
   - Maximum script runtime affects how much data can be processed
   - Limited concurrent executions per Google account

2. **Trigger Limitations**:
   - Limited number of triggers per Google account
   - Specific timing constraints on time-based triggers
   - Potential for missed edit triggers during high-volume edits

3. **Spreadsheet Constraints**:
   - Performance degradation with large data volumes
   - "Phantom rows" issue where empty rows persist in sheet metadata
   - Google Sheets API rate limits

4. **Deployment Constraints**:
   - Scripts are bound to specific spreadsheets
   - Permissions must be granted by each provider
   - Script changes require redeployment

## Dependencies

### External Dependencies
- **Google Workspace**: Access to Google Sheets and Google Apps Script
- **Master Spreadsheet**: Central spreadsheet must exist and be accessible to all scripts
- **Provider Spreadsheets**: Individual provider spreadsheets must follow expected structure
- **Email Notification**: Valid email address for receiving error notifications

### Internal Dependencies
- **Function Naming Conventions**: Consistent naming for triggers and utility functions
- **UUID System**: Reliance on UUID generation and validation for data integrity
- **Setup Process**: Proper initial setup required for scripts to function correctly
- **Trigger Management**: Time-based and edit triggers must be correctly configured

## Security and Access Control

1. **Script Authorization**:
   - Scripts require authorization from users to access spreadsheets
   - Each script is bound to its provider's spreadsheet
   - Master spreadsheet must grant edit access to all provider accounts

2. **Data Security**:
   - UUIDs used to track data without exposing sensitive information
   - Error notifications include minimal data details
   - Logs are stored in protected sheets

3. **Script Permissions**:
   - Scripts require permission to:
     - Read/write to spreadsheets
     - Set up triggers
     - Send emails
     - Access script properties 