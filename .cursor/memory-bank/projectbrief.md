# Project Brief: Unified Dental Scripts

## Overview
This project consists of several Google Apps Script files that synchronize provider production data from individual spreadsheets to a central master spreadsheet. The scripts serve both dentists and hygienists across different practice locations, primarily Humble and Baytown.

## Core Requirements
1. Maintain separate script deployments for each individual provider
2. Ensure data integrity through a UUID-based tracking system
3. Provide reliable and automated data synchronization every 6 hours
4. Handle on-edit events to generate UUIDs for new data entries
5. Maintain consistent error handling and logging
6. Facilitate easy setup and deployment of scripts

## Project Goals
1. Standardize the synchronization system across all providers
2. Improve code maintainability through consistent patterns
3. Minimize manual data entry errors
4. Provide accurate production tracking for business operations
5. Enhance script portability through consistent function naming
6. Support cleanup operations to optimize spreadsheet performance

## Key Components
1. Provider-specific sync scripts (both dentists and hygienists)
2. Consistent structure for handling different data layouts
3. Robust UUID generation and validation system
4. Trigger management for automated operation
5. Error handling and logging
6. Cleanup utility for spreadsheet maintenance 