---
description:
globs:
alwaysApply: true
---
# UUID System for Data Sync Scripts

The UUID (Universally Unique Identifier) system is a critical component of the data sync scripts, ensuring data integrity and preventing duplicate entries.

## How UUIDs Are Used

1. **Unique Row Identification**: Each row in source sheets receives a UUID when it contains valid data
2. **Duplicate Prevention**: The master sheet maintains a record of all UUIDs to prevent duplicate entries
3. **Change Tracking**: UUIDs allow tracking rows across data changes and updates

## UUID Column Placement

- The `UUID_COL_INDEX` constant defines where in the source sheet the UUID is stored
- This is typically set to column O (index 15) for dentist sheets, but can be configured per provider
- The UUID column is automatically added by the `ensureUuidColumnForAllMonths_` function

## UUID Generation Rules

UUIDs are automatically generated when:

1. **During Initialization**: The `seedAllMissingUuids_` function adds UUIDs to all valid existing rows
2. **During Editing**: The `onEditHandler` function adds a UUID when a valid row is edited
3. **Valid Data Required**: UUIDs are only added to rows with:
   - A valid date (not in the future)
   - At least one valid production value (number, including zero and negative values)

## Implementation Details

- The `Utilities.getUuid()` Google Apps Script method generates the UUIDs
- The `onEditHandler` function checks if the edited cell is in a production column
- The sync process skips rows without UUIDs and rows with duplicate UUIDs
- The master sheet maintains the canonical list of all UUIDs across providers

## Troubleshooting

If rows aren't syncing properly, check:
1. Does the row have a UUID? If not, it may not meet the validation criteria
2. Is the UUID duplicated in the master sheet? This would cause it to be skipped
3. Has the UUID column been properly set up in the source sheet? Run the `Setup` function if unsure