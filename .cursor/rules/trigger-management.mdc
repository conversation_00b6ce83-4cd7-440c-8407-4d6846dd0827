---
description:
globs:
alwaysApply: true
---
# Trigger Management System

The trigger management system is responsible for ensuring that sync scripts run automatically at appropriate intervals and in response to edits.

## Types of Triggers Used

1. **Time-Driven Triggers**: Run the `syncToExternalMaster` function on a schedule (typically every 6 hours)
2. **Edit Triggers**: Run the `onEditHandler` function when a cell is edited in the source spreadsheet

## Trigger Management Functions

### `reinstallTrigger_(functionName, hours)`

- Removes any existing time-based triggers for the specified function
- Creates a new time-driven trigger that runs every `hours` hours
- Uses function name constants to ensure portability
- Example: `reinstallTrigger_(SYNC_FUNCTION_NAME, 6);`

### `reinstallEditTrigger_()`

- Removes any existing edit triggers for the `EDIT_HANDLER_FUNCTION_NAME`
- Creates a new edit trigger bound to the specified `SOURCE_SHEET_ID`
- Ensures that only one edit trigger exists for the spreadsheet
- Used in the `Setup` function to ensure correct trigger configuration

### `deleteTriggersByHandler_(functionName)`

- Utility function that removes all triggers associated with a specific handler function
- Called by both `reinstallTrigger_` and `reinstallEditTrigger_`
- Helps prevent duplicate triggers that could lead to data errors

## Setup Process

During the `Setup` function, triggers are configured as follows:

1. All existing triggers for the sync and edit functions are deleted
2. A new time-driven trigger is created to run the sync function every 6 hours
3. A new edit trigger is created to run the edit handler when changes are made

## Troubleshooting

If triggers aren't working properly:

1. Run the `listAllTriggers()` utility function to see all active triggers
2. Verify that the correct handler functions are registered with the triggers
3. Run the `Setup` function again to reinstall triggers if necessary
4. Check that your script has sufficient permissions to create triggers