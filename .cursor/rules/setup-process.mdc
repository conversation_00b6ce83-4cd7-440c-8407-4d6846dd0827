---
description:
globs:
alwaysApply: true
---
# Setup Process for Sync Scripts

The setup process is a critical component of the sync script system, ensuring all prerequisites are in place before syncing begins.

## The Setup Function

The `Setup` function handles the initial configuration of a sync script:

```javascript
function Setup() {
  const functionName = SETUP_FUNCTION_NAME;
  try {
    Logger.log(`Starting ${functionName} for ${PROVIDER_NAME}...`);
    ensureUuidColumnForAllMonths_();
    seedAllMissingUuids_();
    ensureLogSheet_();
    ensureMasterHeaders_();
    reinstallTrigger_(SYNC_FUNCTION_NAME, 6);
    reinstallEditTrigger_();
    Logger.log(`${functionName} completed successfully.`);
    SpreadsheetApp.getUi().alert(`${PROVIDER_NAME} Sync Setup Successful! See Execution Logs for details.`);
  } catch (err) {
    Logger.log(`${functionName} failed: ${err.message}\n${err.stack}`);
    notifyError_(functionN<PERSON>, err);
    SpreadsheetApp.getUi().alert(`${PROVIDER_NAME} Sync Setup FAILED! Check Execution Logs and email for details.`);
  }
}