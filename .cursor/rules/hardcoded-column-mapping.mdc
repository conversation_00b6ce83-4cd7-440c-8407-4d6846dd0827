---
description:
globs:
alwaysApply: true
---
# Hardcoded Column Mapping

<img src="../../logo.svg" alt="Unified Dental Scripts Logo" width="200" height="200" align="center">

## Overview

The Hardcoded Column Mapping approach is used in the [supabase/supabasesync.gs](mdc:supabase/supabasesync.gs) script for reliable, direct mapping of Google Sheet columns to Supabase fields without relying on header row detection.

## Column Mapping

### Fixed Column Positions

The script uses zero-indexed column positions with these exact mappings:

| Column Letter | Index | Field Name in Supabase |
|---------------|-------|------------------------|
| A | 0 | date |
| B | 1 | hours_worked |
| C | 2 | location |
| D | 3 | provider_name |
| E | 4 | provider_type |
| F | 5 | source_sheet |
| G | 6 | production_goal_daily |
| H | 7 | verified_production |
| I | 8 | bonus |
| J | 9 | humble_production |
| K | 10 | baytown_production |
| L | 11 | monthly_goal |
| M | 12 | uuid |



## Implementation

The mapping is implemented directly in the `mapSheetRowToSupabaseRecord_` function:

```javascript
// Date (Column A, Index 0)
const dateValue = rowData[0];
if (dateValue instanceof Date && !Number.isNaN(dateValue.getTime())) {
    record.date = Utilities.formatDate(dateValue, timeZone, "yyyy-MM-dd");
} else {
    record.date = null;
}

// Hours Worked (Column B, Index 1)
record.hours_worked = rowData[1];

// ... and so on for all columns
```

## Benefits

1. **Explicitness**: No ambiguity about which column corresponds to which field
2. **Performance**: No overhead of header scanning or dynamic mapping
3. **Control**: Full control over the data flow at each column position
4. **Simplicity**: Direct index access without intermediate lookups

## Requirements

To maintain this system properly:

1. **Fixed Column Order**: The sheet must maintain these exact column positions
2. **Data Types**: Each column must contain the correct data type for its field
3. **UUID**: Column M must always contain the uuid values
4. **Schema Alignment**: Supabase table fields must match the names used in the mapping

## Handling Changes

If the Google Sheet structure changes:

1. Update the hardcoded indices in `mapSheetRowToSupabaseRecord_`
2. Ensure the column comments are updated to reflect the new positions
3. Update this documentation to maintain accurate reference