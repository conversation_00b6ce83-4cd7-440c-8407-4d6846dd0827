---
description:
globs:
alwaysApply: true
---
# Function Naming Conventions

The sync scripts use a consistent set of function naming conventions to ensure modularity and portability across different provider implementations.

## Core Function Name Constants

Function names are defined as constants in the configuration section of each script:

```javascript
// --- Core Function Names (for triggers, menus, etc.) ---
/** @const {string} Name of the main synchronization function. */
const SYNC_FUNCTION_NAME = 'syncToExternalMaster';
/** @const {string} Name of the function handling onEdit events. */
const EDIT_HANDLER_FUNCTION_NAME = 'onEditHandler';
/** @const {string} Name of the UUID seeding function. */
const SEED_UUIDS_FUNCTION_NAME = 'seedAllMissingUuids_';
/** @const {string} Name of the setup function. */
const SETUP_FUNCTION_NAME = 'Setup';
```