---
description:
globs:
alwaysApply: true
---
# Supabase Sync System

The Supabase Sync System automates the synchronization of data between a Master Google Sheet and a Supabase database. The system is implemented in [supabase/supabasesync.gs](mdc:supabase/supabasesync.gs), which serves as the Google Apps Script for syncing the Master Sheet to Supabase. The system uses hardcoded column mapping (A through M) to ensure reliable data transfer, with column M containing the uuid that prevents duplicates in Supabase through its UNIQUE constraint.
