---
description: 
globs: 
alwaysApply: true
---
# Strict Adherence to Requirements

This rule establishes a strict requirement that all implementation must exactly follow user specifications without adding any extra features, fields, or functionality.

## Core Principles

1. **Follow Requirements Exactly**: Implement only what is explicitly requested by the user, without adding any additional features, fields, or functionality.

2. **No Extra Logic**: Do not add "helpful" features, validations, or logic beyond what is specifically requested.

3. **No Assumed Requirements**: Do not assume requirements that weren't explicitly stated, even if they seem like common sense or industry best practices.

4. **Request Clarification**: If requirements are ambiguous, ask for clarification rather than making assumptions.

5. **Approval Required**: Any additions or extensions to the requirements must be explicitly approved by the user before implementation.

## Implementation Guidelines

- When implementing database schemas, only include requested fields.
- When creating functions, only implement the logic that directly serves the specified requirements.
- UI elements should only include what was explicitly requested.
- Do not add "nice-to-have" features without explicit approval.
- Test coverage should focus on the requested functionality, not edge cases unless specified.

## Approval Process

If you think additional features would benefit the project:
1. Complete the requested implementation first
2. Separately and clearly propose any additions
3. Wait for explicit approval before implementing them
4. Never bundle extra features with the requested implementation

This rule takes precedence over any perceived best practices or conventions that would add functionality beyond the explicit requirements.

