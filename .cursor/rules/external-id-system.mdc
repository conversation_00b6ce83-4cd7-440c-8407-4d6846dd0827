---
description:
globs:
alwaysApply: true
---
# UUID System

<img src="../../logo.svg" alt="Unified Dental Scripts Logo" width="200" height="200" align="center">

## Overview

The UUID System is a critical component of the data synchronization framework used across the Unified Dental Scripts project. It provides a reliable mechanism for tracking and uniquely identifying rows of data as they move between Google Sheets and external databases like Supabase.

## Key Concepts

### Purpose
- Creates a **stable, unique identifier** for each row of production data
- Enables **reliable synchronization** between spreadsheets and databases
- Prevents **duplicate entries** during data transfer operations
- Allows for proper **updating of existing records** rather than creating duplicates

### Implementation in Google Sheets

The `uuid` is stored in column M and is generated using the `Utilities.getUuid()` function when a new row is added or when running the seed function.

### Database Implementation

In the Supabase database, the `uuid` column:
- Is defined as a UUID column
- Has a UNIQUE constraint applied:
  ```sql
  ALTER TABLE public.all_production_live_sheet
  ADD CONSTRAINT all_production_live_sheet_uuid_unique
  UNIQUE (uuid);
  ```
- Serves as the key for upsert operations

### Hard-Coded Access in the Sync Script

1. Google Sheet generates the `uuid` via `Utilities.getUuid()`
2. The [supabase/supabasesync.gs](mdc:supabase/supabasesync.gs) script reads this column
3. The script sends data to Supabase with the `Prefer: resolution=merge-duplicates` header
4. Supabase uses the `uuid` to determine if it should:
   - Insert a new record (if the ID doesn't exist yet)
   - Update an existing record (if the ID already exists)

## Best Practices

- Never manually modify the `uuid` values
- Ensure the UUID generation is working in all production sheets
- Verify that the `uuid` column header exists exactly as spelled
- Add the appropriate UNIQUE constraint in your database tables
- Use consistent column naming across systems (always `uuid`)

## Troubleshooting

- **Missing IDs**: Check if the date column has valid entries
- **Duplicate Errors**: Ensure the UNIQUE constraint exists in the database
- **Sync Failures**: Verify the `uuid` column is properly mapped in the sync script
- **Data Loss**: Never delete the `uuid` column or its values