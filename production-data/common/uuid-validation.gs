/**
 * Common UUID validation functions for production data sync system.
 * These functions ensure that UUIDs are properly generated and validated
 * throughout the data flow from source sheets to master sheet to Supabase.
 */

/**
 * Validates that a row from a source sheet has a valid UUID before being added to the master sheet.
 * If the row is missing a UUID, logs a warning and returns false.
 * 
 * @param {Array} row The row data from the source sheet
 * @param {number} uuidColIndex The 0-based index of the UUID column in the row
 * @param {string} sheetName The name of the source sheet
 * @param {number} rowNum The row number in the source sheet (for logging)
 * @param {string} functionName The name of the calling function (for logging)
 * @returns {boolean} Whether the row has a valid UUID
 */
function validateSourceRowUuid(row, uuidColIndex, sheetName, rowNum, functionName) {
  const uuid = row[uuidColIndex];
  
  if (!uuid) {
    Logger.log(`WARNING in ${functionName}: Row ${rowNum} in sheet "${sheetName}" is missing a UUID. ` +
               `UUIDs should be generated in source sheets, not in the master sheet. ` +
               `This row will be skipped during sync.`);
    return false;
  }
  
  // Validate UUID format (basic check)
  if (typeof uuid !== 'string' || uuid.length < 32) {
    Logger.log(`WARNING in ${functionName}: Row ${rowNum} in sheet "${sheetName}" has an invalid UUID format: ${uuid}. ` +
               `This row will be skipped during sync.`);
    return false;
  }
  
  return true;
}

/**
 * Checks if a row in the master sheet is missing a UUID and logs a warning.
 * This is used to detect rows that might have been added directly to the master sheet
 * without going through the proper source sheet → master sheet flow.
 * 
 * @param {Array} row The row data from the master sheet
 * @param {number} uuidColIndex The 0-based index of the UUID column in the row
 * @param {number} rowNum The row number in the master sheet (for logging)
 * @param {string} functionName The name of the calling function (for logging)
 * @returns {boolean} Whether the row has a valid UUID
 */
function validateMasterRowUuid(row, uuidColIndex, rowNum, functionName) {
  const uuid = row[uuidColIndex];
  
  if (!uuid) {
    Logger.log(`WARNING in ${functionName}: Row ${rowNum} in master sheet is missing a UUID. ` +
               `UUIDs should only be generated in source sheets, not in the master sheet. ` +
               `This row will be skipped during sync to Supabase.`);
    return false;
  }
  
  return true;
}
