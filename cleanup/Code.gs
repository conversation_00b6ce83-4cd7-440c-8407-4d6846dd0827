/**
 * Batch cleanup: delete phantom rows in multiple spreadsheets,
 * logging each action into each sheet’s own "Cleanup‑Logs" tab.
 */

// ─────────────── CONFIGURATION ───────────────

// List every production‑data spreadsheet you want to clean
const PRODUCTION_SHEET_IDS = [
  '1tBm7LX8-ELkUMooP8dMrawWWue1cR7tSMHN9OQPGHRc', // Chi
  // add more IDs here...
];

// Name of the per‑spreadsheet log tab
const CLEANUP_LOG_TAB = 'Cleanup‑Logs';

// ─────────── HELPERS ──────────

/** Matches tabs named like "Jan‑24", "Feb2025", etc. */
function isMonthlySheet(name) {
  return /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[-\s]?\d{2,4}$/i
         .test(name.trim());
}

/** Ensure the Cleanup‑Logs sheet exists and has headers. */
function ensureCleanupLogSheet_(ss) {
  let logSh = ss.getSheetByName(CLEANUP_LOG_TAB);
  if (!logSh) {
    logSh = ss.insertSheet(CLEANUP_LOG_TAB);
    logSh.appendRow(['Timestamp','Sheet','Rows Deleted','Kept Through Row']);
  }
  return logSh;
}

/** Append one record to the Cleanup‑Logs sheet of that spreadsheet. */
function logCleanup_(ss, sheetName, deletedCount, keptThrough) {
  const logSh = ensureCleanupLogSheet_(ss);
  logSh.appendRow([new Date(), sheetName, deletedCount, keptThrough]);
}

// ─────────── MAIN BATCH CLEANUP ──────────

/**
 * For each ID in PRODUCTION_SHEET_IDS:
 *  • Opens that spreadsheet
 *  • Runs cleanupPhantomRowsOn(ss)
 */
function cleanupAllProductionSheets() {
  PRODUCTION_SHEET_IDS.forEach(id => {
    try {
      const ss = SpreadsheetApp.openById(id);
      cleanupPhantomRowsOn(ss);
    } catch (e) {
      // Log in your personal execution log if opening fails
      Logger.log(`Error opening ${id}: ${e.message}`);
    }
  });
}

/**
 * Cleanup routine for a single Spreadsheet object:
 *  • Scans each "monthly" tab
 *  • Deletes any rows after the last real data row
 *  • Logs per‑sheet summary into its Cleanup‑Logs tab
 */
function cleanupPhantomRowsOn(ss) {
  const sheets = ss.getSheets().filter(sh => isMonthlySheet(sh.getName()));

  sheets.forEach(sh => {
    const sheetName = sh.getName();
    const lastRow   = sh.getLastRow();

    // If there's no data section, skip & log
    if (lastRow < 3) {
      Logger.log(`"${sheetName}": no data rows (lastRow=${lastRow})`);
      logCleanup_(ss, sheetName, 0, lastRow);
      return;
    }

    // Fetch rows 3 → lastRow
    const data = sh.getRange(3, 1, lastRow - 2, sh.getLastColumn())
                   .getValues();

    // Find true last data row by checking Date / Hours / Verified col
    let trueLast = 2;
    for (let i = data.length - 1; i >= 0; i--) {
      const [date, hoursWorked, , verifiedProd] = data[i];
      if ((date instanceof Date && !isNaN(date)) ||
          (hoursWorked  !== '' && hoursWorked  != null) ||
          (verifiedProd !== '' && verifiedProd != null)) {
        trueLast = i + 3;  // convert data‑array index to sheet row
        break;
      }
    }

    if (trueLast < lastRow) {
      const numDeleted = lastRow - trueLast;
      sh.deleteRows(trueLast + 1, numDeleted);
      Logger.log(`"${sheetName}": trimmed ${numDeleted} rows (kept through ${trueLast})`);
      logCleanup_(ss, sheetName, numDeleted, trueLast);
    } else {
      Logger.log(`"${sheetName}": no phantom rows (lastRow=${lastRow})`);
      logCleanup_(ss, sheetName, 0, lastRow);
    }
  });
}