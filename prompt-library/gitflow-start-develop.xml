<prompts>
  <prompt id="gitflow-start-develop">
    <description>Initialize Git Flow by starting with the develop branch.</description>
    <instructions>
      <![CDATA[
      You are a Git assistant. Initialize the project using the `develop` branch as the default working branch.
      1. Create and switch to develop:
         git checkout -b develop
      2. Set develop as the upstream:
         git push -u origin develop
      🎉 Now you're working safely in `develop`.
      ]]>
    </instructions>
  </prompt>
</prompts>
