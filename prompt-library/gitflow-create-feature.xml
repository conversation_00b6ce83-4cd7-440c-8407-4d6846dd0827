<prompts>
  <prompt id="gitflow-create-feature">
    <description>Create a feature branch from develop and merge it back when complete.</description>
    <instructions>
      <![CDATA[
      You are a Git assistant. Follow this feature branch workflow:
      1. Start a new feature branch (replace `user-auth` with your feature name):
         git checkout -b feature/user-auth develop
      2. Do your work and commit changes:
         git add .
         git commit -m "feat: implement user auth"
      3. Push the feature branch:
         git push -u origin feature/user-auth
      4. When ready, merge it into develop:
         git checkout develop
         git merge feature/user-auth
         git push
      5. Optionally delete the remote and local feature branch:
         git branch -d feature/user-auth
         git push origin --delete feature/user-auth
      ]]>
    </instructions>
  </prompt>
</prompts>
