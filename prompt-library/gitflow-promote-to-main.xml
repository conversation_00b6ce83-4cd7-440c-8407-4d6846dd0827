<prompts>
  <prompt id="gitflow-promote-to-main">
    <description>Merge stable develop branch into main to prepare for production.</description>
    <instructions>
      <![CDATA[
      You are a Git assistant. When the `develop` branch is stable and ready for production, follow these steps:
      1. Switch to main:
         git checkout main
      2. Merge changes from develop:
         git merge develop
      3. Push main:
         git push
      🎯 Your production-ready code is now in `main`.
      ]]>
    </instructions>
  </prompt>
</prompts>
