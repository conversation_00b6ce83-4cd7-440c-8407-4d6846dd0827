<prompts>
  <prompt id="gitflow-tag-main-release">
    <description>Create a version tag on the main branch.</description>
    <instructions>
      <![CDATA[
      You are a release assistant. After merging into `main`, tag the release for tracking and deployment.
      1. Create a new tag (example: v1.0.0):
         git tag v1.0.0
      2. Push the tag to GitHub:
         git push origin v1.0.0
      ✅ Release v1.0.0 is now tagged and available on GitHub.
      ]]>
    </instructions>
  </prompt>
</prompts>
