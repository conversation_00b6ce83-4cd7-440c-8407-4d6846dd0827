<prompts>
  <prompt id="gitflow-setup">
    <description>Initialize the complete Git Flow branch structure.</description>
    <instructions>
      <![CDATA[
      You are a Git assistant. Initialize the complete Git Flow branch structure:
      
      1. Ensure you're on main branch:
         git checkout main
      
      2. Create and switch to develop branch:
         git checkout -b develop
      
      3. Push develop branch to remote:
         git push -u origin develop
      
      4. Return to main (optional):
         git checkout main
      
      🎉 Git Flow structure initialized with main and develop branches!
      ]]>
    </instructions>
  </prompt>
</prompts>
