/**
 * Setup and initialization functions for the Unscheduled Treatment System.
 */

/**
 * Main setup function for the Unscheduled Treatment System.
 * Creates necessary sheets and installs triggers.
 */
function SetupUnscheduledTx() {
  return withUserFeedback(() => {
    Logger.log(`Starting ${SETUP_FUNCTION_NAME}...`);
    
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    
    // Ensure all required sheets exist
    const masterSheet = ensureSheetExists(spreadsheet, SHEET_NAME_MASTER);
    ensureSheetExists(spreadsheet, SHEET_NAME_SCHEDULED);
    ensureSheetExists(spreadsheet, SHEET_NAME_DISCONNECTED);
    ensureSheetExists(spreadsheet, SHEET_NAME_NO_TREATMENT);
    ensureSheetExists(spreadsheet, SHEET_NAME_WILL_CALL_BACK);
    ensureSheetExists(spreadsheet, SHEET_NAME_FOLLOW_UP);
    ensureSheetExists(spreadsheet, SHEET_NAME_DNC);
    ensureSheetExists(spreadsheet, SHEET_NAME_NO_ANSWER);
    ensureSheetExists(spreadsheet, SHEET_NAME_SUMMARY);
    ensureSheetExists(spreadsheet, SHEET_NAME_ERROR_LOG);

    // Validate required columns in master sheet
    validateRequiredColumns(masterSheet);

    // Store column indices in script properties for faster access
    const indices = getRequiredColumnIndices(masterSheet);
    storeColumnIndices_(indices);

    // Setup triggers
    deleteExistingTriggers_();
    createEditTrigger_();
    createBatchTrigger_();

    Logger.log(`${SETUP_FUNCTION_NAME} completed successfully`);
    return true;
  }, SETUP_FUNCTION_NAME, true)();
}

/**
 * Stores column indices in script properties.
 * @param {Object} indices - The column indices to store
 * @private
 */
function storeColumnIndices_(indices) {
  const properties = PropertiesService.getScriptProperties();
  properties.setProperty('COLUMN_INDICES', JSON.stringify(indices));
  Logger.log('Stored column indices in script properties:', indices);
}

/**
 * Gets stored column indices from script properties.
 * @return {Object} The stored column indices
 */
function getStoredColumnIndices() {
  const properties = PropertiesService.getScriptProperties();
  const indicesJson = properties.getProperty('COLUMN_INDICES');
  if (!indicesJson) {
    throw new Error('Column indices not found in script properties. Please run Setup.');
  }
  return JSON.parse(indicesJson);
}

/**
 * Deletes all existing triggers for this script.
 * @private
 */
function deleteExistingTriggers_() {
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === ON_EDIT_HANDLER_FUNCTION_NAME ||
        trigger.getHandlerFunction() === BATCH_PROCESS_FUNCTION_NAME) {
      ScriptApp.deleteTrigger(trigger);
      Logger.log(`Deleted existing trigger for ${trigger.getHandlerFunction()}`);
    }
  });
}

/**
 * Creates the edit trigger for handling status changes.
 * @private
 */
function createEditTrigger_() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  ScriptApp.newTrigger(ON_EDIT_HANDLER_FUNCTION_NAME)
    .forSpreadsheet(spreadsheet)
    .onEdit()
    .create();
  Logger.log(`Created edit trigger for ${ON_EDIT_HANDLER_FUNCTION_NAME}`);
}

/**
 * Creates the time-driven trigger for batch processing.
 * @private
 */
function createBatchTrigger_() {
  ScriptApp.newTrigger(BATCH_PROCESS_FUNCTION_NAME)
    .timeBased()
    .everyDays(1)
    .atHour(1) // Run at 1 AM
    .create();
  Logger.log(`Created daily trigger for ${BATCH_PROCESS_FUNCTION_NAME}`);
}

/**
 * Lists all current triggers for debugging.
 */
function listTriggers() {
  return withErrorHandler(() => {
    const triggers = ScriptApp.getProjectTriggers();
    const triggerInfo = triggers.map(trigger => ({
      functionName: trigger.getHandlerFunction(),
      eventType: trigger.getEventType(),
      source: trigger.getTriggerSource()
    }));
    Logger.log('Current triggers:', triggerInfo);
    return triggerInfo;
  }, 'listTriggers')();
}

/**
 * Validates the setup of the unscheduled treatment system.
 * @return {Object} Validation results
 */
function validateSetup() {
  return withErrorHandler(() => {
    const results = {
      sheets: {},
      triggers: {},
      columnIndices: null,
      overall: false
    };

    // Check sheets
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const requiredSheets = [
      SHEET_NAME_MASTER,
      SHEET_NAME_SCHEDULED,
      SHEET_NAME_DISCONNECTED,
      SHEET_NAME_NO_TREATMENT,
      SHEET_NAME_WILL_CALL_BACK,
      SHEET_NAME_FOLLOW_UP,
      SHEET_NAME_DNC,
      SHEET_NAME_NO_ANSWER,
      SHEET_NAME_SUMMARY,
      SHEET_NAME_ERROR_LOG
    ];

    requiredSheets.forEach(sheetName => {
      results.sheets[sheetName] = spreadsheet.getSheetByName(sheetName) !== null;
    });

    // Check triggers
    const triggers = ScriptApp.getProjectTriggers();
    results.triggers.edit = triggers.some(t => t.getHandlerFunction() === ON_EDIT_HANDLER_FUNCTION_NAME);
    results.triggers.batch = triggers.some(t => t.getHandlerFunction() === BATCH_PROCESS_FUNCTION_NAME);

    // Check column indices
    try {
      results.columnIndices = getStoredColumnIndices();
    } catch (e) {
      results.columnIndices = null;
    }

    // Overall status
    results.overall = Object.values(results.sheets).every(Boolean) &&
                     Object.values(results.triggers).every(Boolean) &&
                     results.columnIndices !== null;

    Logger.log('Setup validation results:', results);
    return results;
  }, 'validateSetup')();
} 