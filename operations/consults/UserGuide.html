<!DOCTYPE html>
<html>
<head>
  <base target="_blank">
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unscheduled Treatments System - User Guide</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      color: #333;
      line-height: 1.6;
    }
    .logo-container {
      text-align: center;
      margin-bottom: 20px;
    }
    .logo {
      width: 200px;
      height: 200px;
    }
    h1 {
      color: #2c5282;
      border-bottom: 2px solid #e2e8f0;
      padding-bottom: 10px;
    }
    h2 {
      color: #3182ce;
      margin-top: 25px;
      padding-top: 10px;
      border-top: 1px solid #e2e8f0;
    }
    h3 {
      color: #4299e1;
      margin-top: 20px;
    }
    ul, ol {
      padding-left: 30px;
    }
    li {
      margin-bottom: 8px;
    }
    .note {
      background-color: #ebf8ff;
      border-left: 4px solid #4299e1;
      padding: 10px 15px;
      margin: 15px 0;
    }
    .important {
      background-color: #fed7d7;
      border-left: 4px solid #f56565;
      padding: 10px 15px;
      margin: 15px 0;
    }
    code {
      background-color: #f7fafc;
      padding: 2px 5px;
      border-radius: 3px;
      font-family: monospace;
      font-size: 0.9em;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 15px 0;
    }
    th, td {
      border: 1px solid #e2e8f0;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f7fafc;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f7fafc;
    }
  </style>
</head>
<body>
  <div class="logo-container">
    <img src="../../logo.svg" alt="Unified Dental Scripts Logo" width="200" height="200" class="logo">
  </div>
  
  <h1>Unscheduled Treatments System - User Guide</h1>
  
  <h2>Table of Contents</h2>
  <ol>
    <li><a href="#introduction">Introduction</a></li>
    <li><a href="#setup">Initial Setup</a></li>
    <li><a href="#structure">System Structure</a></li>
    <li><a href="#daily">Daily Usage</a></li>
    <li><a href="#filtering">Filtering Calls</a></li>
    <li><a href="#troubleshooting">Troubleshooting</a></li>
  </ol>
  
  <h2 id="introduction">1. Introduction</h2>
  <p>The Unscheduled Treatments System helps track and manage patient follow-up calls for unscheduled treatments. It automatically routes patient records to appropriate sheets based on call outcomes and provides tools for prioritizing follow-up calls.</p>
  
  <h2 id="setup">2. Initial Setup</h2>
  <p>To set up the Unscheduled Treatments System for the first time:</p>
  <ol>
    <li>Click on <code>Unscheduled Tx Tools</code> > <code>Setup Tracker</code></li>
    <li>The system will create all necessary sheets and set up required triggers</li>
    <li>A confirmation message will appear when setup is complete</li>
  </ol>
  <div class="note">
    <strong>Note:</strong> If you don't see the menu, refresh the page. If it still doesn't appear, contact your administrator.
  </div>
  
  <h2 id="structure">3. System Structure</h2>
  
  <h3>Main Sheets</h3>
  <table>
    <tr>
      <th>Sheet Name</th>
      <th>Purpose</th>
    </tr>
    <tr>
      <td>Master</td>
      <td>Main working sheet containing all active unscheduled treatment calls</td>
    </tr>
    <tr>
      <td>completed</td>
      <td>Treatments that have been completed</td>
    </tr>
    <tr>
      <td>follow up</td>
      <td>Patients requiring follow-up calls</td>
    </tr>
    <tr>
      <td>disconnected</td>
      <td>Patients with disconnected phone numbers</td>
    </tr>
    <tr>
      <td>dnc</td>
      <td>Do Not Call list (includes duplicates)</td>
    </tr>
    <tr>
      <td>will call back</td>
      <td>Patients who said they will call back</td>
    </tr>
    <tr>
      <td>scheduled</td>
      <td>Patients who scheduled their treatment</td>
    </tr>
    <tr>
      <td>no treatment</td>
      <td>Patients who decided not to proceed with treatment</td>
    </tr>
    <tr>
      <td>tx on going</td>
      <td>Patients with ongoing treatment</td>
    </tr>
    <tr>
      <td>no answer</td>
      <td>Patients who didn't answer after the third attempt</td>
    </tr>
    <tr>
      <td>NUMBER OF UNSCHEDULED TX</td>
      <td>Summary statistics of all cases by status</td>
    </tr>
    <tr>
      <td>Error Log</td>
      <td>System errors and troubleshooting information</td>
    </tr>
  </table>
  
  <h3>Key Status Values</h3>
  <p>The system recognizes these exact status values (case-sensitive):</p>
  <ul>
    <li><code>completed</code> - Moves row to "completed" sheet</li>
    <li><code>dnc</code> - Moves row to "dnc" sheet</li>
    <li><code>duplicate</code> - Moves row to "dnc" sheet</li>
    <li><code>no treatment</code> - Moves row to "no treatment" sheet</li>
    <li><code>follow up</code> - Moves row to "follow up" sheet</li>
    <li><code>tx on going</code> - Moves row to "tx on going" sheet</li>
    <li><code>scheduled</code> - Moves row to "scheduled" sheet</li>
    <li><code>no answer</code> - When selected in Attempt 3 Notes, moves row to "no answer" sheet</li>
    <li><code>disconnected</code> - Moves row to "disconnected" sheet</li>
    <li><code>will call back</code> - Moves row to "will call back" sheet</li>
  </ul>
  
  <h2 id="daily">4. Daily Usage</h2>
  
  <h3>Adding New Patient Records</h3>
  <ol>
    <li>Add new patient information to the <strong>Master</strong> sheet</li>
    <li>Fill in patient details, including next attempt and third attempt due dates</li>
    <li>Ensure all required columns are completed</li>
  </ol>
  
  <h3>Updating Call Outcomes</h3>
  <ol>
    <li>After contacting a patient, update the <strong>Status</strong> column with the appropriate status value</li>
    <li>The system will automatically move the row to the correct sheet based on the status</li>
    <li>For third attempts with no answer, select <code>no answer</code> in the <strong>ATTEMPT 3 NOTES</strong> column</li>
  </ol>
  
  <h3>Running Batch Processing</h3>
  <p>To process all records at once (useful after making multiple updates):</p>
  <ol>
    <li>Click on <code>Unscheduled Tx Tools</code> > <code>Process All Records Now</code></li>
    <li>The system will scan all records and route them to appropriate sheets</li>
  </ol>
  
  <h2 id="filtering">5. Filtering Calls</h2>
  
  <h3>Priority Filtering</h3>
  <p>The system offers tools to help prioritize which patients to call next:</p>
  
  <h4>Filter by Due Date</h4>
  <ol>
    <li>Click on <code>Unscheduled Tx Tools</code> > <code>View Priority Calls</code> > <code>Show Calls Due Soonest</code></li>
    <li>The Master sheet will be filtered to show calls with the nearest due dates first</li>
  </ol>
  
  <h4>Filter by Status Type</h4>
  <p>You can filter by specific status types:</p>
  <ul>
    <li>For insurance status: Click on <code>Unscheduled Tx Tools</code> > <code>View Priority Calls</code> > <code>Show Insurance Status</code></li>
    <li>For financial status: Click on <code>Unscheduled Tx Tools</code> > <code>View Priority Calls</code> > <code>Show Financial Status</code></li>
    <li>For disconnected numbers: Click on <code>Unscheduled Tx Tools</code> > <code>View Priority Calls</code> > <code>Show Disconnected Status</code></li>
    <li>For callback requests: Click on <code>Unscheduled Tx Tools</code> > <code>View Priority Calls</code> > <code>Show Will Call Back Status</code></li>
  </ul>
  
  <h4>Filter First Attempts</h4>
  <ol>
    <li>Click on <code>Unscheduled Tx Tools</code> > <code>View Priority Calls</code> > <code>Show First Attempts (No Status)</code></li>
    <li>The Master sheet will be filtered to show patients who haven't been contacted yet (empty status)</li>
  </ol>
  
  <h3>Clearing Filters</h3>
  <p>To remove filters and see all records again:</p>
  <ol>
    <li>Click on <code>Unscheduled Tx Tools</code> > <code>Clear Filter</code></li>
    <li>The filter will be removed from the Master sheet</li>
  </ol>
  <div class="note">
    <strong>Note:</strong> You can also clear filters using the filter button at the top of any filtered column and selecting "Clear filter" from the dropdown menu.
  </div>
  
  <h2 id="troubleshooting">6. Troubleshooting</h2>
  
  <h3>Common Issues</h3>
  
  <h4>Menu Not Appearing</h4>
  <ol>
    <li>Refresh the page</li>
    <li>If the menu still doesn't appear, go to <code>Extensions</code> > <code>Apps Script</code></li>
    <li>Run the <code>onOpen</code> function manually</li>
    <li>Return to the spreadsheet and check for the menu</li>
  </ol>
  
  <h4>Record Not Moving to Expected Sheet</h4>
  <ol>
    <li>Check that the <strong>Status</strong> value exactly matches one of the recognized values (case-sensitive)</li>
    <li>Run <code>Process All Records Now</code> from the menu</li>
    <li>Check the Error Log sheet for any related errors</li>
  </ol>
  
  <h4>Filters Not Working</h4>
  <ol>
    <li>Make sure the sheet has data beyond the header row</li>
    <li>Check that column headers haven't been changed</li>
    <li>Try running <code>Setup Tracker</code> again to reset the system</li>
  </ol>
  
  <h4>Understanding Error Messages</h4>
  <p>If you encounter an error message, here's what it might mean:</p>
  <table>
    <tr>
      <th>Error Message</th>
      <th>What It Means</th>
      <th>What To Do</th>
    </tr>
    <tr>
      <td>The main worksheet is missing</td>
      <td>The Master sheet cannot be found</td>
      <td>Run Setup Tracker from the menu</td>
    </tr>
    <tr>
      <td>There are no patient records to filter</td>
      <td>The Master sheet is empty or contains only headers</td>
      <td>Add patient data to the Master sheet</td>
    </tr>
    <tr>
      <td>The system needs to be set up before using this feature</td>
      <td>Initial setup has not been completed or configuration is missing</td>
      <td>Run Setup Tracker from the menu</td>
    </tr>
    <tr>
      <td>There was a problem with filtering your data</td>
      <td>The filter couldn't be applied correctly</td>
      <td>Try clearing any existing filters and try again</td>
    </tr>
    <tr>
      <td>You don't have permission to perform this action</td>
      <td>You lack the necessary permission for this operation</td>
      <td>Contact your administrator for access</td>
    </tr>
  </table>
  
  <div class="important">
    <strong>Important:</strong> If you encounter persistent issues, check the Error Log sheet for details and contact your system administrator.
  </div>
</body>
</html> 