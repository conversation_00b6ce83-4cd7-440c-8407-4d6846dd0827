/**
 * Priority filtering functions for the Unscheduled Treatment System.
 */

/**
 * Gets an existing filter or creates a new one on the master sheet.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to get or create a filter on
 * @return {GoogleAppsScript.Spreadsheet.Filter} The sheet's filter
 * @private
 */
function getOrCreateFilterOnSheet_(sheet) {
  // Check if a filter already exists
  let filter = sheet.getFilter();
  
  // If no filter exists, create one
  if (!filter) {
    const lastRow = sheet.getLastRow();
    const lastCol = sheet.getLastColumn();
    if (lastRow > 1) {
      const range = sheet.getRange(1, 1, lastRow, lastCol);
      filter = range.createFilter();
    } else {
      throw new Error('No data to filter');
    }
  }
  
  return filter;
}

/**
 * Clears the current filter from the master sheet.
 * @return {Object} Result of the operation
 */
function clearFilter() {
  return withUserFeedback(() => {
    const masterSheet = getSheetByName(SHEET_NAME_MASTER);
    if (!masterSheet) {
      throw new Error('Master sheet not found');
    }

    const filter = masterSheet.getFilter();
    if (filter) {
      filter.remove();
      masterSheet.setActiveRange(masterSheet.getRange(1, 1));
      return {
        success: true,
        message: 'Filter cleared successfully'
      };
    }
    
    return {
      success: true,
      message: 'No filter to clear'
    };
  }, 'clearFilter', true)();
}

/**
 * Filters rows by soonest due date.
 * @return {Object} Result of the filtering operation
 */
function filterBySoonestDueDate() {
  return withUserFeedback(() => {
    const masterSheet = getSheetByName(SHEET_NAME_MASTER);
    if (!masterSheet) {
      throw new Error('Master sheet not found');
    }

    // Get column indices
    const columnIndices = getStoredColumnIndices();
    
    // Clear any existing filter
    if (masterSheet.getFilter()) {
      masterSheet.getFilter().remove();
    }
    
    // Get the data range
    const lastRow = masterSheet.getLastRow();
    const lastCol = masterSheet.getLastColumn();
    if (lastRow <= 1) {
      return {
        success: true,
        message: 'No data to filter'
      };
    }
    
    // Create a filter
    const range = masterSheet.getRange(1, 1, lastRow, lastCol);
    const filter = range.createFilter();
    
    // Apply sort: first by Next Attempt Due column (ascending)
    const nextAttemptDueCol = columnIndices.nextAttemptDue + 1; // Convert to 1-based
    if (nextAttemptDueCol) {
      filter.sort(nextAttemptDueCol, true); // true = ascending
    }
    
    // Then by Third Attempt Due column (ascending)
    const thirdAttemptDueCol = columnIndices.thirdAttemptDue + 1; // Convert to 1-based
    if (thirdAttemptDueCol) {
      filter.sort(thirdAttemptDueCol, true); // true = ascending
    }
    
    // Scroll to the top
    masterSheet.setActiveRange(masterSheet.getRange(2, 1));

    return {
      success: true,
      message: 'Applied filter by due date (soonest first)'
    };
  }, 'filterBySoonestDueDate', true)();
}

/**
 * Filters rows by specific status values.
 * @param {Array<string>} statusValues - Array of status values to filter by
 * @return {Object} Result of the filtering operation
 */
function filterByStatus(statusValues) {
  return withUserFeedback(() => {
    const masterSheet = getSheetByName(SHEET_NAME_MASTER);
    if (!masterSheet) {
      throw new Error('Master sheet not found');
    }

    // Get column indices
    const columnIndices = getStoredColumnIndices();
    
    // Clear any existing filter
    if (masterSheet.getFilter()) {
      masterSheet.getFilter().remove();
    }
    
    // Get the data range
    const lastRow = masterSheet.getLastRow();
    const lastCol = masterSheet.getLastColumn();
    if (lastRow <= 1) {
      return {
        success: true,
        message: 'No data to filter'
      };
    }
    
    // Create a filter
    const range = masterSheet.getRange(1, 1, lastRow, lastCol);
    const filter = range.createFilter();
    
    // Apply filter criteria to status column
    const statusCol = columnIndices.status + 1; // Convert to 1-based
    const normalizedStatusValues = statusValues.map(s => s.toLowerCase());
    
    // Create filter criteria for the status column
    const criteria = SpreadsheetApp.newFilterCriteria()
      .whenTextEqualTo(statusValues) // Original case-sensitive values
      .build();
    
    filter.setColumnFilterCriteria(statusCol, criteria);
    
    // Scroll to the top
    masterSheet.setActiveRange(masterSheet.getRange(2, 1));
    
    const statusList = statusValues.join(', ');
    return {
      success: true,
      message: `Applied filter for status: ${statusList}`
    };
  }, 'filterByStatus', true)();
}

/**
 * Filters rows with missing (empty) status.
 * @return {Object} Result of the filtering operation
 */
function filterByMissingStatus() {
  return withUserFeedback(() => {
    const masterSheet = getSheetByName(SHEET_NAME_MASTER);
    if (!masterSheet) {
      throw new Error('Master sheet not found');
    }

    // Get column indices
    const columnIndices = getStoredColumnIndices();
    
    // Clear any existing filter
    if (masterSheet.getFilter()) {
      masterSheet.getFilter().remove();
    }
    
    // Get the data range
    const lastRow = masterSheet.getLastRow();
    const lastCol = masterSheet.getLastColumn();
    if (lastRow <= 1) {
      return {
        success: true,
        message: 'No data to filter'
      };
    }
    
    // Create a filter
    const range = masterSheet.getRange(1, 1, lastRow, lastCol);
    const filter = range.createFilter();
    
    // Apply filter criteria to show only empty status cells
    const statusCol = columnIndices.status + 1; // Convert to 1-based
    const criteria = SpreadsheetApp.newFilterCriteria()
      .whenCellEmpty()
      .build();
    
    filter.setColumnFilterCriteria(statusCol, criteria);
    
    // Scroll to the top
    masterSheet.setActiveRange(masterSheet.getRange(2, 1));

    return {
      success: true,
      message: 'Applied filter for missing status'
    };
  }, 'filterByMissingStatus', true)();
}

/**
 * Filters rows with "insurance" status.
 * @return {Object} Result of the filtering operation
 */
function filterByInsuranceStatus() {
  return withUserFeedback(() => {
    return filterByStatus(["insurance"]);
  }, 'filterByInsuranceStatus', true)();
}

/**
 * Filters rows with "disconnected" status.
 * @return {Object} Result of the filtering operation
 */
function filterByDisconnectedStatus() {
  return withUserFeedback(() => {
    return filterByStatus(["disconnected"]);
  }, 'filterByDisconnectedStatus', true)();
}

/**
 * Filters rows with "will call back" status.
 * @return {Object} Result of the filtering operation
 */
function filterByWillCallBackStatus() {
  return withUserFeedback(() => {
    return filterByStatus(["will call back"]);
  }, 'filterByWillCallBackStatus', true)();
}

/**
 * Filters rows with "financial" status.
 * @return {Object} Result of the filtering operation
 */
function filterByFinancialStatus() {
  return withUserFeedback(() => {
    return filterByStatus(["financial"]);
  }, 'filterByFinancialStatus', true)();
} 