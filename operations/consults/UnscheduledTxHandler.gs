/**
 * Core processing logic for the Unscheduled Treatment System.
 */

/**
 * <PERSON>les edit events in the spreadsheet.
 * @param {GoogleAppsScript.Spreadsheet.SpreadsheetEventObject} e - The edit event object
 */
function handleEditTrigger(e) {
  return withErrorHandler(() => {
    if (!e || !e.range) {
      Logger.log('No edit event or range information found');
      return;
    }

    const range = e.range;
    const sheet = range.getSheet();
    
    // Only process edits in the Master sheet
    if (sheet.getName() !== SHEET_NAME_MASTER) {
      return;
    }

    const editedValue = String(range.getValue()).trim().toLowerCase();
    const columnIndices = getStoredColumnIndices();
    const column = range.getColumn() - 1; // Convert to 0-based index

    // Only process edits in Status or Attempt 3 Notes columns
    if (column !== columnIndices.status && column !== columnIndices.attempt3Notes) {
      return;
    }

    processRowAndRoute_(sheet, range.getRow(), editedValue, column, columnIndices);
  }, 'handleEditTrigger')();
}

/**
 * Processes all rows in the Master sheet.
 */
function processAllUnscheduledTxRows() {
  return withUserFeedback(() => {
    const masterSheet = getSheetByName(SHEET_NAME_MASTER);
    if (!masterSheet) {
      throw new Error('Master sheet not found');
    }

    const columnIndices = getStoredColumnIndices();
    const data = getSheetDataExcludingHeader(masterSheet);
    
    // Process from bottom up to handle deletions safely
    for (let i = data.length - 1; i >= 0; i--) {
      const rowData = data[i];
      const rowNumber = i + 2; // Add 2 to account for 0-based index and header row
      
      const status = String(rowData[columnIndices.status] || '').trim().toLowerCase();
      const attempt3Notes = String(rowData[columnIndices.attempt3Notes] || '').trim().toLowerCase();
      
      // Process status column first, then attempt3Notes if no status match
      if (status) {
        processRowAndRoute_(masterSheet, rowNumber, status, columnIndices.status, columnIndices);
      } else if (attempt3Notes) {
        processRowAndRoute_(masterSheet, rowNumber, attempt3Notes, columnIndices.attempt3Notes, columnIndices);
      }
    }

    return true;
  }, 'processAllUnscheduledTxRows', true)();
}

/**
 * Processes a single row and routes it to the appropriate sheet based on status.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sourceSheet - The source sheet
 * @param {number} rowNumber - The 1-based row number
 * @param {string} value - The value to check for routing
 * @param {number} columnIndex - The 0-based column index of the edited cell
 * @param {Object} columnIndices - Map of column names to indices
 * @private
 */
function processRowAndRoute_(sourceSheet, rowNumber, value, columnIndex, columnIndices) {
  // Determine target sheet based on status value
  let targetSheetName = null;
  const normalizedValue = value.toLowerCase();

  if (columnIndex === columnIndices.status) {
    // Check status column values
    switch (normalizedValue) {
      case STATUS_VALUE_COMPLETED:
        targetSheetName = SHEET_NAME_COMPLETED;
        break;
      case STATUS_VALUE_DNC:
      case STATUS_VALUE_DUPLICATE:
        targetSheetName = SHEET_NAME_DNC;
        break;
      case STATUS_VALUE_NO_TREATMENT:
        targetSheetName = SHEET_NAME_NO_TREATMENT;
        break;
      case STATUS_VALUE_FOLLOW_UP:
        targetSheetName = SHEET_NAME_FOLLOW_UP;
        break;
      case STATUS_VALUE_TX_ON_GOING:
        targetSheetName = SHEET_NAME_TX_ON_GOING;
        break;
      case STATUS_VALUE_SCHEDULED:
        targetSheetName = SHEET_NAME_SCHEDULED;
        break;
    }
  } else if (columnIndex === columnIndices.attempt3Notes) {
    // Check Attempt 3 Notes column for no answer status
    if (normalizedValue === STATUS_VALUE_NO_ANSWER) {
      targetSheetName = SHEET_NAME_NO_ANSWER;
    }
  }

  // If we found a matching status, move the row
  if (targetSheetName) {
    const targetSheet = ensureSheetExists(sourceSheet.getParent(), targetSheetName);
    moveRow(sourceSheet, rowNumber, targetSheet);
    updateSummary_();
  }
}

/**
 * Updates the summary sheet with current counts.
 * @private
 */
function updateSummary_() {
  return withErrorHandler(() => {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const summarySheet = ensureSheetExists(spreadsheet, SHEET_NAME_SUMMARY);
    
    // Clear existing data except headers
    clearSheetExceptHeader(summarySheet);

    // Get counts from each sheet
    const sheetCounts = [
      { name: SHEET_NAME_MASTER, label: 'Unscheduled Treatment' },
      { name: SHEET_NAME_SCHEDULED, label: 'Scheduled' },
      { name: SHEET_NAME_DISCONNECTED, label: 'Disconnected' },
      { name: SHEET_NAME_NO_TREATMENT, label: 'No Treatment' },
      { name: SHEET_NAME_WILL_CALL_BACK, label: 'Will Call Back' },
      { name: SHEET_NAME_FOLLOW_UP, label: 'Follow Up' },
      { name: SHEET_NAME_DNC, label: 'DNC' },
      { name: SHEET_NAME_NO_ANSWER, label: 'No Answer' }
    ].map(({ name, label }) => {
      const sheet = spreadsheet.getSheetByName(name);
      const count = sheet ? Math.max(0, sheet.getLastRow() - 1) : 0;
      return [label, count];
    });

    // Update summary sheet
    if (sheetCounts.length > 0) {
      summarySheet.getRange(2, 1, sheetCounts.length, 2).setValues(sheetCounts);
    }

    Logger.log('Updated summary counts');
  }, 'updateSummary_')();
} 