/**
 * Utility functions for finding column indices dynamically.
 */

/**
 * Finds the index of a column by its header name.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to search in
 * @param {string} headerName - The name of the header to find
 * @param {boolean} [required=false] - Whether to throw an error if the column is not found
 * @return {number} The 0-based index of the column, or -1 if not found
 * @throws {Error} If the column is required but not found
 */
function findColumnIndex(sheet, headerName, required = false) {
  return withErrorHandler(() => {
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const index = headers.findIndex(header => 
      String(header).trim().toLowerCase() === headerName.trim().toLowerCase()
    );

    if (required && index === -1) {
      throw new Error(`Required column "${headerName}" not found in sheet "${sheet.getName()}"`);
    }

    return index;
  }, 'findColumnIndex')();
}

/**
 * Gets all required column indices for the unscheduled treatment system.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to search in
 * @return {Object} An object mapping header names to their column indices
 * @throws {Error} If any required column is not found
 */
function getRequiredColumnIndices(sheet) {
  return withErrorHandler(() => {
    // Required columns that must be present
    const requiredColumns = {
      status: HEADER_STATUS,
      nextAttemptDue: HEADER_NEXT_ATTEMPT_DUE,
      thirdAttemptDue: HEADER_3RD_ATTEMPT_DUE,
      attempt3Notes: HEADER_ATTEMPT_3_NOTES,
      patientName: HEADER_PATIENT_NAME
    };

    // Find indices for required columns
    const indices = {};
    for (const [key, headerName] of Object.entries(requiredColumns)) {
      indices[key] = findColumnIndex(sheet, headerName, true); // true means required
    }

    // Log column mapping for debugging
    Logger.log('Column indices found:', indices);

    return indices;
  }, 'getRequiredColumnIndices')();
}

/**
 * Validates that all required columns are present in a sheet.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to validate
 * @return {boolean} True if all required columns are present
 * @throws {Error} If any required column is missing
 */
function validateRequiredColumns(sheet) {
  return withErrorHandler(() => {
    const indices = getRequiredColumnIndices(sheet);
    const missingColumns = [];

    // Check required columns
    for (const [key, index] of Object.entries(indices)) {
      if (index === -1) {
        missingColumns.push(key);
      }
    }

    if (missingColumns.length > 0) {
      throw new Error(
        `Missing required columns in sheet "${sheet.getName()}": ${missingColumns.join(', ')}`
      );
    }

    return true;
  }, 'validateRequiredColumns')();
}

/**
 * Gets the column letter for a given index.
 * @param {number} index - The 0-based column index
 * @return {string} The column letter (A, B, C, ..., Z, AA, AB, etc.)
 */
function getColumnLetter_(index) {
  let temp = index;
  let letter = '';
  while (temp >= 0) {
    letter = String.fromCharCode((temp % 26) + 65) + letter;
    temp = Math.floor(temp / 26) - 1;
  }
  return letter;
}

/**
 * Gets a human-readable column reference.
 * @param {number} index - The 0-based column index
 * @return {string} The column reference (e.g., "Column D (Status)")
 */
function getColumnReference_(index) {
  return `Column ${getColumnLetter_(index)} (${index + 1})`;
} 