/**
 * Utility functions for error handling and logging.
 */

/**
 * Logs an error with detailed information.
 * @param {string} functionName - The name of the function where the error occurred
 * @param {Error} error - The error object
 * @param {string} [message] - Optional additional message
 */
function logError(functionName, error, message = '') {
  const errorDetails = {
    timestamp: new Date().toISOString(),
    function: functionName,
    message: message || error.message,
    stack: error.stack,
    additionalInfo: message ? error.message : ''
  };

  // Log to Apps Script logger
  Logger.log(`ERROR in ${functionName}: ${JSON.stringify(errorDetails, null, 2)}`);

  // Log to error sheet
  try {
    const errorSheet = ensureSheetExists(SpreadsheetApp.getActiveSpreadsheet(), SHEET_NAME_ERROR_LOG);
    const headers = ['Timestamp', 'Function', 'Message', 'Stack Trace', 'Additional Info'];
    
    // Ensure headers exist
    if (errorSheet.getLastRow() === 0) {
      errorSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    }

    // Add error row
    errorSheet.appendRow([
      errorDetails.timestamp,
      errorDetails.function,
      errorDetails.message,
      errorDetails.stack,
      errorDetails.additionalInfo
    ]);
  } catch (loggingError) {
    // If error logging fails, at least try to log to console
    console.error('Failed to log error to sheet:', loggingError);
    console.error('Original error:', errorDetails);
  }
}

/**
 * Sends an email notification for critical errors.
 * @param {string} functionName - The name of the function where the error occurred
 * @param {Error} error - The error object
 * @param {string} [message] - Optional additional message
 */
function notifyError(functionName, error, message = '') {
  try {
    const user = Session.getActiveUser().getEmail();
    const subject = `Unscheduled Treatment System Error in ${functionName}`;
    const body = `
Error occurred in the Unscheduled Treatment System

Function: ${functionName}
Time: ${new Date().toLocaleString()}
User: ${user}
Message: ${message || error.message}
Stack Trace:
${error.stack}

Additional Info:
${message ? error.message : 'None'}

This is an automated notification. Please check the Error Log sheet for more details.
`;

    MailApp.sendEmail({
      to: user,
      subject: subject,
      body: body
    });
  } catch (emailError) {
    console.error('Failed to send error notification email:', emailError);
  }
}

/**
 * Higher-order function that wraps a function with error handling.
 * @param {Function} fn - The function to wrap
 * @param {string} functionName - The name of the function for error logging
 * @return {Function} The wrapped function
 */
function withErrorHandler(fn, functionName) {
  return function(...args) {
    try {
      return fn.apply(this, args);
    } catch (error) {
      logError(functionName, error);
      notifyError(functionName, error);
      throw error; // Re-throw to maintain error propagation
    }
  };
}

/**
 * Wraps a function with error handling and user feedback.
 * @param {Function} fn - The function to wrap
 * @param {string} functionName - The name of the function for error logging
 * @param {boolean} [showSuccess=false] - Whether to show a success message
 * @return {Function} The wrapped function
 */
function withUserFeedback(fn, functionName, showSuccess = false) {
  return function(...args) {
    const ui = SpreadsheetApp.getUi();
    try {
      const result = fn.apply(this, args);
      if (showSuccess && result.success) {
        ui.alert('Success', result.message || 'Operation completed successfully.', ui.ButtonSet.OK);
      }
      return result;
    } catch (error) {
      // Log the technical error details for troubleshooting
      logError(functionName, error);
      notifyError(functionName, error);
      
      // Show a user-friendly error message
      let friendlyMessage = getFriendlyErrorMessage(error, functionName);
      
      ui.alert(
        'Something went wrong',
        friendlyMessage,
        ui.ButtonSet.OK
      );
      
      // Return a friendly error object rather than throwing
      return {
        success: false,
        message: friendlyMessage
      };
    }
  };
}

/**
 * Converts technical error messages to user-friendly language.
 * @param {Error} error - The original error object
 * @param {string} functionName - The function where the error occurred
 * @return {string} A user-friendly error message
 */
function getFriendlyErrorMessage(error, functionName) {
  // Get the original error message
  const originalMessage = error.message;
  
  // Map common technical errors to friendly messages
  if (originalMessage.includes('Master sheet not found')) {
    return 'The main worksheet is missing. Please run Setup Tracker from the menu to fix this issue.';
  }
  
  if (originalMessage.includes('No data to filter')) {
    return 'There are no patient records to filter. Please add data to the Master sheet first.';
  }
  
  if (originalMessage.includes('Column indices') || originalMessage.includes('getStoredColumnIndices')) {
    return 'The system needs to be set up before using this feature. Please run Setup Tracker from the menu.';
  }
  
  if (originalMessage.includes('filter') || originalMessage.includes('Filter')) {
    return 'There was a problem with filtering your data. Try clearing the filter and trying again.';
  }
  
  if (originalMessage.includes('Access denied') || originalMessage.includes('Permission')) {
    return 'You don\'t have permission to perform this action. Please contact your administrator.';
  }
  
  // Default friendly message
  return 'The system encountered an unexpected issue. The problem has been logged. Please try again or contact your administrator if the issue persists.';
}

/**
 * Validates that a value exists and is of the expected type.
 * @param {*} value - The value to validate
 * @param {string} name - The name of the value for error messages
 * @param {string|string[]} expectedType - The expected type(s) of the value
 * @throws {Error} If validation fails
 */
function validateValue(value, name, expectedType) {
  if (value === undefined || value === null) {
    throw new Error(`${name} is required but was not provided`);
  }

  const types = Array.isArray(expectedType) ? expectedType : [expectedType];
  const actualType = Array.isArray(value) ? 'array' : typeof value;
  
  if (!types.includes(actualType)) {
    throw new Error(
      `${name} must be of type ${types.join(' or ')}, but got ${actualType}`
    );
  }
} 