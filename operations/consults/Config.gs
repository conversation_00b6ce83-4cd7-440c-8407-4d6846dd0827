/**
 * Configuration constants for the Unscheduled Treatment System.
 * This file centralizes all configuration values used across the system.
 */

// Sheet Names
const SHEET_NAME_MASTER = "Master";
const SHEET_NAME_COMPLETED = "completed";
const SHEET_NAME_FOLLOW_UP = "follow up";
const SHEET_NAME_DISCONNECTED = "disconnected";
const SHEET_NAME_DNC = "dnc";
const SHEET_NAME_WILL_CALL_BACK = "will call back";
const SHEET_NAME_SCHEDULED = "scheduled";
const SHEET_NAME_NO_TREATMENT = "no treatment";
const SHEET_NAME_TX_ON_GOING = "tx on going";
const SHEET_NAME_NO_ANSWER = "no answer";
const SHEET_NAME_SUMMARY = "NUMBER OF UNSCHEDULED TX";
const SHEET_NAME_ERROR_LOG = "Error Log";

// Column Headers
const HEADER_STATUS = "Status";
const HEADER_NEXT_ATTEMPT_DUE = "Next Attempt Due on:";
const HEADER_3RD_ATTEMPT_DUE = "3rd Attempt due on:";
const HEADER_ATTEMPT_3_NOTES = "ATTEMPT 3 NOTES - Please select the outcome.";
const HEADER_PATIENT_NAME = "Patient Name";

// Status Values for Routing
const STATUS_VALUE_UNSCHEDULED_TX = "UNSCHEDULED TX";
const STATUS_VALUE_COMPLETED = "completed";
const STATUS_VALUE_DNC = "dnc";
const STATUS_VALUE_NO_TREATMENT = "no treatment";
const STATUS_VALUE_FOLLOW_UP = "follow up";
const STATUS_VALUE_TX_ON_GOING = "tx on going";
const STATUS_VALUE_DUPLICATE = "duplicate"; // Routes to DNC
const STATUS_VALUE_SCHEDULED = "scheduled";
const STATUS_VALUE_NO_ANSWER = "no answer"; // From Attempt 3 Notes column
const STATUS_VALUE_DISCONNECTED = "disconnected";
const STATUS_VALUE_WILL_CALL_BACK = "will call back";

// Function Names
const SETUP_FUNCTION_NAME = 'SetupUnscheduledTx';
const ON_EDIT_HANDLER_FUNCTION_NAME = 'handleEditTrigger';
const BATCH_PROCESS_FUNCTION_NAME = 'processAllUnscheduledTxRows';
const FILTER_BY_DATE_FUNCTION_NAME = 'filterBySoonestDueDate';
const FILTER_BY_STATUS_FUNCTION_NAME = 'filterByStatus';
const FILTER_BY_MISSING_STATUS_FUNCTION_NAME = 'filterByMissingStatus';
const FILTER_BY_INSURANCE_STATUS_FUNCTION_NAME = 'filterByInsuranceStatus';
const FILTER_BY_FINANCIAL_STATUS_FUNCTION_NAME = 'filterByFinancialStatus';
const FILTER_BY_DISCONNECTED_STATUS_FUNCTION_NAME = 'filterByDisconnectedStatus';
const FILTER_BY_WILL_CALL_BACK_STATUS_FUNCTION_NAME = 'filterByWillCallBackStatus';

// Column Indices (0-based)
const STATUS_COLUMN_D = 3; // Column D
const STATUS_COLUMN_L = 11; // Column L

// Time Settings
const TIMEZONE = SpreadsheetApp.getActiveSpreadsheet().getSpreadsheetTimeZone();
const DATE_FORMAT = "yyyy-MM-dd";
const BATCH_PROCESS_INTERVAL_HOURS = 6;

// Error Handling Settings
const ERROR_NOTIFICATION_EMAIL = ""; // Add email if needed
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_MS = 1000;

// Filter Settings
const TEMP_FILTER_SHEET_PREFIX = "Filtered Results";
const MAX_DAYS_TO_KEEP_FILTERED = 7; // Auto-delete filtered sheets older than this 