/**
 * Main entry point for the Unscheduled Treatment System.
 * Creates the custom menu when the spreadsheet is opened.
 */
function onOpen() {
  try {
    const ui = SpreadsheetApp.getUi();
    const menu = ui.createMenu('Unscheduled Tx Tools')
      // Core functions
      .addItem('Setup Tracker', SETUP_FUNCTION_NAME)
      .addItem('Process All Records Now', BATCH_PROCESS_FUNCTION_NAME)
      .addSeparator()
      // Priority filtering submenu
      .addSubMenu(ui.createMenu('View Priority Calls')
        .addItem('Show Calls Due Soonest', FILTER_BY_DATE_FUNCTION_NAME)
        .addItem('Show Insurance Status', FILTER_BY_INSURANCE_STATUS_FUNCTION_NAME)
        .addItem('Show Financial Status', FILTER_BY_FINANCIAL_STATUS_FUNCTION_NAME)
        .addItem('Show Disconnected Status', FILTER_BY_DISCONNECTED_STATUS_FUNCTION_NAME)
        .addItem('Show Will Call Back Status', FILTER_BY_WILL_CALL_BACK_STATUS_FUNCTION_NAME)
        .addItem('Show First Attempts (No Status)', FILTER_BY_MISSING_STATUS_FUNCTION_NAME))
      .addItem('Clear Filter', 'clearFilter')
      .addSeparator()
      // Help and documentation
      .addItem('Show User Guide', 'showUserGuide');
    
    menu.addToUi();
    Logger.log('Successfully created Unscheduled Tx Tools menu');
  } catch (error) {
    Logger.log(`Error creating menu: ${error.message}\n${error.stack}`);
  }
} 