/**
 * Utility functions for common sheet operations.
 */

/**
 * Gets a sheet by name.
 * @param {string} name - The name of the sheet to get
 * @return {GoogleAppsScript.Spreadsheet.Sheet|null} The requested sheet or null if not found
 */
function getSheetByName(name) {
  return withErrorHandler(() => {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(name);
    if (!sheet) {
      Logger.log(`Sheet "${name}" not found`);
    }
    return sheet;
  }, 'getSheetByName')();
}

/**
 * Gets a sheet by name, creating it if it doesn't exist.
 * @param {GoogleAppsScript.Spreadsheet.Spreadsheet} spreadsheet - The spreadsheet containing the sheet
 * @param {string} name - The name of the sheet to get or create
 * @return {GoogleAppsScript.Spreadsheet.Sheet} The requested sheet
 */
function ensureSheetExists(spreadsheet, name) {
  return withErrorHandler(() => {
    let sheet = spreadsheet.getSheetByName(name);
    if (!sheet) {
      sheet = spreadsheet.insertSheet(name);
      Logger.log(`Created new sheet: ${name}`);
    }
    return sheet;
  }, 'ensureSheetExists')();
}

/**
 * Appends a row to a sheet.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to append to
 * @param {Array} rowData - The data to append
 * @return {GoogleAppsScript.Spreadsheet.Range} The appended range
 */
function appendRow(sheet, rowData) {
  return withErrorHandler(() => {
    const lastRow = sheet.getLastRow();
    const range = sheet.getRange(lastRow + 1, 1, 1, rowData.length);
    range.setValues([rowData]);
    Logger.log(`Appended row to sheet "${sheet.getName()}" at row ${lastRow + 1}`);
    return range;
  }, 'appendRow')();
}

/**
 * Deletes a row from a sheet.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to delete from
 * @param {number} rowNumber - The 1-based row number to delete
 */
function deleteRow(sheet, rowNumber) {
  return withErrorHandler(() => {
    sheet.deleteRow(rowNumber);
    Logger.log(`Deleted row ${rowNumber} from sheet "${sheet.getName()}"`);
  }, 'deleteRow')();
}

/**
 * Gets the data range of a sheet.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to get data from
 * @return {GoogleAppsScript.Spreadsheet.Range} The data range
 */
function getDataRange(sheet) {
  return withErrorHandler(() => {
    return sheet.getDataRange();
  }, 'getDataRange')();
}

/**
 * Gets values from a range.
 * @param {GoogleAppsScript.Spreadsheet.Range} range - The range to get values from
 * @return {Array<Array>} The values in the range
 */
function getValues(range) {
  return withErrorHandler(() => {
    return range.getValues();
  }, 'getValues')();
}

/**
 * Gets the header row values from a sheet.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to get headers from
 * @return {Array} The header values
 */
function getHeaders(sheet) {
  return withErrorHandler(() => {
    const headerRange = sheet.getRange(1, 1, 1, sheet.getLastColumn());
    return headerRange.getValues()[0];
  }, 'getHeaders')();
}

/**
 * Copies a row from one sheet to another.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sourceSheet - The sheet to copy from
 * @param {number} sourceRowNumber - The 1-based row number to copy
 * @param {GoogleAppsScript.Spreadsheet.Sheet} targetSheet - The sheet to copy to
 * @return {GoogleAppsScript.Spreadsheet.Range} The new range containing the copied data
 */
function copyRow(sourceSheet, sourceRowNumber, targetSheet) {
  return withErrorHandler(() => {
    const sourceRange = sourceSheet.getRange(sourceRowNumber, 1, 1, sourceSheet.getLastColumn());
    const sourceData = sourceRange.getValues();
    return appendRow(targetSheet, sourceData[0]);
  }, 'copyRow')();
}

/**
 * Moves a row from one sheet to another (copies then deletes original).
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sourceSheet - The sheet to move from
 * @param {number} sourceRowNumber - The 1-based row number to move
 * @param {GoogleAppsScript.Spreadsheet.Sheet} targetSheet - The sheet to move to
 * @return {GoogleAppsScript.Spreadsheet.Range} The new range containing the moved data
 */
function moveRow(sourceSheet, sourceRowNumber, targetSheet) {
  return withErrorHandler(() => {
    const newRange = copyRow(sourceSheet, sourceRowNumber, targetSheet);
    deleteRow(sourceSheet, sourceRowNumber);
    Logger.log(`Moved row ${sourceRowNumber} from "${sourceSheet.getName()}" to "${targetSheet.getName()}"`);
    return newRange;
  }, 'moveRow')();
}

/**
 * Gets all data from a sheet excluding the header row.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to get data from
 * @return {Array<Array>} The data values (excluding headers)
 */
function getSheetDataExcludingHeader(sheet) {
  return withErrorHandler(() => {
    const dataRange = sheet.getDataRange();
    const values = dataRange.getValues();
    return values.slice(1); // Exclude header row
  }, 'getSheetDataExcludingHeader')();
}

/**
 * Clears all data from a sheet except the header row.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - The sheet to clear
 */
function clearSheetExceptHeader(sheet) {
  return withErrorHandler(() => {
    const lastRow = sheet.getLastRow();
    if (lastRow > 1) {
      sheet.getRange(2, 1, lastRow - 1, sheet.getLastColumn()).clear();
      Logger.log(`Cleared data from sheet "${sheet.getName()}" (preserved header row)`);
    }
  }, 'clearSheetExceptHeader')();
} 