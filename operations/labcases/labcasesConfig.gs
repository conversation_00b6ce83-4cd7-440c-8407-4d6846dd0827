/**
 * @fileoverview Configuration module for the Lab Cases Management System.
 * Contains all configurable settings, sheet names, column mappings, and status values.
 */

/**
 * Default sheet names used in the Lab Cases Management System.
 * These can be customized during setup.
 */
const DEFAULT_SHEET_NAMES = {
  SOURCE: "LAB CASES",
  COMPLETED: "Completed Cases",
  ATTENTION: "Attention Required",
  RETURN: "Return",
  CANCELLED: "Cancelled Cases",
  REDO: "Re-do Cases",
  SCHEDULED_PAID: "Scheduled + Paid"
};

/**
 * Default column headers used in the Lab Cases Management System.
 * These can be customized during setup.
 */
const DEFAULT_COLUMN_HEADERS = {
  PATIENT_NAME: "Patient Name (First, Last)",
  INSURANCE_STATUS: "INSURANCE STATUS",
  APPOINTMENT_STATUS: "APPOINTMENT STATUS",
  LAST_EDITED: "Last Edited",
  CASE_TYPE: "CASE TYPE",
  SEAT_DATE: "SEAT DATE",
  DOCTOR: "Doctor",
  TOTAL_FEE: "Total Fee"
};

/**
 * Default status values that trigger row movement and their corresponding destination sheets.
 */
const STATUS_VALUES = {
  APPOINTMENT: {
    COMPLETED: "COMPLETED",
    ATTENTION: "ATTENTION NEEDED",
    REDO: "RE-DO",
    CANCELLED: "CANCELLED",
    RETURN: "RETURN",
    SCHEDULED: "SCHEDULED"
  },
  INSURANCE: {
    PAID: "PAID"
  }
};

/**
 * Default row where headers are located.
 */
const DEFAULT_HEADER_ROW = 1;

/**
 * Default column indices for important columns.
 * These will be dynamically calculated based on headers.
 */
const DEFAULT_COLUMN_INDICES = {
  PATIENT_NAME: null,
  INSURANCE_STATUS: null,
  APPOINTMENT_STATUS: null,
  LAST_EDITED: null,
  CASE_TYPE: null,
  SEAT_DATE: null,
  DOCTOR: null,
  TOTAL_FEE: null
};

/**
 * Error log headers for the Error Log sheet.
 */
const ERROR_LOG_HEADERS = [
  "Timestamp",
  "Function",
  "Error Message",
  "Error Stack",
  "Context",
  "User"
];
/**
 * Default allowed doctor values for the Doctor dropdown.
 */
const DEFAULT_ALLOWED_DOCTORS = ['DKAM', 'DOBI', 'DCHI'];

/**
 * Get the configuration for the Lab Cases Management System.
 * @param {Object} customConfig - Optional custom configuration to override defaults.
 * @returns {Object} The merged configuration.
 */
function getLabCasesConfig(customConfig = {}) {
  return {
    SHEET_NAMES: { ...DEFAULT_SHEET_NAMES, ...(customConfig.SHEET_NAMES || {}) },
    COLUMN_HEADERS: { ...DEFAULT_COLUMN_HEADERS, ...(customConfig.COLUMN_HEADERS || {}) },
    COLUMN_INDICES: { ...DEFAULT_COLUMN_INDICES, ...(customConfig.COLUMN_INDICES || {}) },
    STATUS_VALUES: { ...STATUS_VALUES, ...(customConfig.STATUS_VALUES || {}) },
    HEADER_ROW: customConfig.HEADER_ROW || DEFAULT_HEADER_ROW,
    ERROR_LOG_HEADERS: customConfig.ERROR_LOG_HEADERS || ERROR_LOG_HEADERS,
    ALLOWED_DOCTORS: customConfig.ALLOWED_DOCTORS || DEFAULT_ALLOWED_DOCTORS
  };
}
