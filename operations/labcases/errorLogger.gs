/**
 * @fileoverview Error logging utilities for the Lab Cases Management System.
 * Provides functions for logging errors and wrapping functions with error handling.
 */

/**
 * Logs an error to the Error Log sheet and console.
 * Creates the Error Log sheet if it doesn't exist.
 *
 * @param {string} functionName - The name of the function where the error occurred.
 * @param {Error|string} error - The error object or error message.
 * @param {Object} context - Additional context about the error (optional).
 * @param {string} [spreadsheetId=null] - Optional spreadsheet ID. If not provided, uses active spreadsheet.
 * @returns {void}
 */
function logError(functionName, error, context = {}, spreadsheetId = null) {
  try {
    // Get the spreadsheet
    const ss = spreadsheetId ?
      SpreadsheetApp.openById(spreadsheetId) :
      SpreadsheetApp.getActiveSpreadsheet();

    if (!ss) {
      console.error(`Could not open spreadsheet for error logging`);
      console.error(`Original error in ${functionName}: ${error.message || error}`);
      return;
    }

    // Get or create error log sheet
    let errorSheet = ss.getSheetByName("Error Log");

    if (!errorSheet) {
      errorSheet = ss.insertSheet("Error Log");
      errorSheet.appendRow(ERROR_LOG_HEADERS);
      errorSheet.getRange(1, 1, 1, ERROR_LOG_HEADERS.length).setFontWeight("bold");
    }

    // Format error object
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : "No stack trace available";

    // Log the error
    const timestamp = new Date();

    // Get user email safely - avoid Session.getActiveUser() which requires additional permissions
    let user = "Unknown";
    try {
      // This is safer and doesn't require additional permissions
      user = Session.getEffectiveUser().getEmail() || "Unknown";
    } catch (e) {
      console.error("Could not get user email:", e);
    }

    const contextStr = JSON.stringify(context);

    errorSheet.appendRow([
      timestamp,
      functionName,
      errorMessage,
      errorStack,
      contextStr,
      user
    ]);

    // Also log to console
    console.error(`ERROR in ${functionName}: ${errorMessage}`);
    if (errorStack) console.error(errorStack);

  } catch (logError) {
    // Last resort if error logging fails
    console.error("Failed to log error:", logError);
    console.error(`Original error in ${functionName}:`, error);
  }
}

/**
 * Wraps a function with error handling.
 * If the function throws an error, it will be logged and optionally re-thrown.
 *
 * @param {Function} fn - The function to wrap.
 * @param {string} functionName - The name of the function (for logging).
 * @param {boolean} [rethrow=true] - Whether to re-throw the error after logging.
 * @param {string} [spreadsheetId=null] - Optional spreadsheet ID for error logging.
 * @returns {Function} The wrapped function.
 */
function withErrorHandling(fn, functionName, rethrow = true, spreadsheetId = null) {
  return function(...args) {
    try {
      return fn.apply(this, args);
    } catch (error) {
      logError(functionName, error, { arguments: args }, spreadsheetId);
      if (rethrow) {
        throw error;
      }
    }
  };
}

/**
 * Base custom error class with enhanced features.
 * @class
 * @extends Error
 */
class BaseError extends Error {
  /**
   * @param {string} name - Error type name.
   * @param {string} message - Error message.
   * @param {object} [details={}] - Optional additional details.
   * @param {Error} [cause] - Optional cause of this error.
   */
  constructor(name, message, details = {}, cause) {
    super(message);
    this.name = name;
    this.details = details;
    this.cause = cause;
    this.timestamp = new Date().toISOString();
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Get a formatted representation of the error.
   * @returns {string} Formatted error string.
   */
  toString() {
    let result = `[${this.name}] ${this.message}`;
    if (Object.keys(this.details).length > 0) {
      result += ` Details: ${JSON.stringify(this.details)}`;
    }
    if (this.cause) {
      result += ` Caused by: ${this.cause.toString()}`;
    }
    return result;
  }
}

/**
 * ValidationError - For input validation failures.
 * @class
 * @extends BaseError
 */
class ValidationError extends BaseError {
  /**
   * @param {string} message - Error message.
   * @param {object} [details={}] - Optional additional details.
   * @param {Error} [cause] - Optional cause of this error.
   */
  constructor(message, details = {}, cause) {
    super('ValidationError', message, details, cause);
  }
}

/**
 * SheetOperationError - For sheet operation failures.
 * @class
 * @extends BaseError
 */
class SheetOperationError extends BaseError {
  /**
   * @param {string} message - Error message.
   * @param {object} [details={}] - Optional additional details.
   * @param {Error} [cause] - Optional cause of this error.
   */
  constructor(message, details = {}, cause) {
    super('SheetOperationError', message, details, cause);
  }
}

/**
 * NotFoundError - For missing resources (sheets, rows).
 * @class
 * @extends BaseError
 */
class NotFoundError extends BaseError {
  /**
   * @param {string} message - Error message.
   * @param {object} [details={}] - Optional additional details.
   * @param {Error} [cause] - Optional cause of this error.
   */
  constructor(message, details = {}, cause) {
    super('NotFoundError', message, details, cause);
  }
}
