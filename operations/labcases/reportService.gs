/**
 * @fileoverview Report service for aggregating and reporting lab case fees per doctor.
 */

/**
 * Generates a monthly fee report aggregated by doctor.
 * @param {number} month - Month number (1-12).
 * @param {number} year - Four-digit year.
 * @returns {Object} Result object with success flag, message, and data map.
 */
function generateDoctorFeeReport(month, year) {
  try {
    const config = getLabCasesConfig();
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const allowedDoctors = config.ALLOWED_DOCTORS;
    const completedSheet = getSheetByName(ss, config.SHEET_NAMES.COMPLETED);
    const headerRow = config.HEADER_ROW;
    const columnIndices = getColumnIndices(completedSheet, config.COLUMN_HEADERS, headerRow);
    // Ensure required columns
    validateRequiredColumns(columnIndices, ['DOCTOR', 'TOTAL_FEE', 'SEAT_DATE']);

    const lastRow = completedSheet.getLastRow();
    if (lastRow <= headerRow) {
      return { success: true, message: 'No completed cases to report.', data: {} };
    }
    const lastCol = completedSheet.getLastColumn();
    const rows = completedSheet.getRange(headerRow + 1, 1, lastRow - headerRow, lastCol).getValues();

    // Aggregate fees per doctor
    const totals = {};
    rows.forEach(row => {
      const rawDate = row[columnIndices.SEAT_DATE - 1];
      const rawDoctor = row[columnIndices.DOCTOR - 1];
      const rawFee = row[columnIndices.TOTAL_FEE - 1];
      const doctor = rawDoctor && rawDoctor.toString().trim();
      let fee = parseFloat(rawFee);
      if (isNaN(fee)) fee = 0;
      // Parse and validate date
      const date = rawDate instanceof Date ? rawDate : new Date(rawDate);
      if (
        doctor &&
        allowedDoctors.includes(doctor) &&
        date instanceof Date && !isNaN(date.getTime()) &&
        date.getFullYear() === year &&
        (date.getMonth() + 1) === month
      ) {
        totals[doctor] = (totals[doctor] || 0) + fee;
      }
    });

    // Prepare summary sheet
    const summaryName = 'Doctor Fee Summary';
    let summarySheet = ss.getSheetByName(summaryName);
    if (!summarySheet) {
      summarySheet = ss.insertSheet(summaryName);
      summarySheet.appendRow(['Month', 'Doctor', 'Total Fee']);
      summarySheet.getRange(1, 1, 1, 3).setFontWeight('bold');
    } else {
      // Clear old data
      const existing = summarySheet.getLastRow();
      if (existing > 1) {
        summarySheet.getRange(2, 1, existing - 1, 3).clearContent();
      }
    }

    // Write report rows
    const period = `${year}-${String(month).padStart(2, '0')}`;
    Object.keys(totals).forEach(doc => {
      summarySheet.appendRow([period, doc, totals[doc]]);
    });

    return { success: true, message: `Report generated for ${period}`, data: totals };
  } catch (error) {
    logError('generateDoctorFeeReport', error);
    return { success: false, message: error.message || String(error) };
  }
}

/**
 * Menu handler for generating the doctor fee report via UI prompts.
 */
function generateDoctorFeeReportMenu() {
  try {
    const ui = SpreadsheetApp.getUi();
    const monthRes = ui.prompt('Generate Doctor Fee Report', 'Enter month (1-12):', ui.ButtonSet.OK_CANCEL);
    if (monthRes.getSelectedButton() !== ui.Button.OK) return;
    const yearRes = ui.prompt('Generate Doctor Fee Report', 'Enter year (e.g. 2025):', ui.ButtonSet.OK_CANCEL);
    if (yearRes.getSelectedButton() !== ui.Button.OK) return;
    const month = parseInt(monthRes.getResponseText(), 10);
    const year = parseInt(yearRes.getResponseText(), 10);
    if (isNaN(month) || month < 1 || month > 12) {
      ui.alert('Invalid month'); return;
    }
    if (isNaN(year) || year < 1900) {
      ui.alert('Invalid year'); return;
    }
    const result = generateDoctorFeeReport(month, year);
    ui.alert(result.success ? 'Report Generated' : 'Error', result.message, ui.ButtonSet.OK);
  } catch (error) {
    logError('generateDoctorFeeReportMenu', error);
    try {
      SpreadsheetApp.getUi().alert('Error', error.message || String(error), ui.ButtonSet.OK);
    } catch (uiErr) {
      console.error('Failed to show alert:', uiErr);
    }
  }
}
/**
 * Scheduled trigger entry point: runs report for the previous month automatically.
 */
function runMonthlyDoctorReport() {
  try {
    const now = new Date();
    const prev = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const month = prev.getMonth() + 1;
    const year = prev.getFullYear();
    const result = generateDoctorFeeReport(month, year);
    if (!result.success) {
      logError('runMonthlyDoctorReport', new Error(result.message));
    }
  } catch (error) {
    logError('runMonthlyDoctorReport', error);
  }
}