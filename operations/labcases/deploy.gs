/**
 * @fileoverview Deployment script for the Lab Cases Management System.
 * This script helps deploy the modular system to different locations.
 */

/**
 * Deploys the Lab Cases Management System to a specific location.
 *
 * @param {string} location - The location to deploy to (e.g., 'baytown', 'humble').
 * @param {string} spreadsheetId - The ID of the spreadsheet to deploy to.
 * @returns {Object} Deployment result with success status and message.
 */
function deploy(location, spreadsheetId = null) {
  try {
    // Check if location is provided
    if (!location) {
      return {
        success: false,
        message: 'Location is required'
      };
    }

    // If no spreadsheet ID provided, get from environment
    if (!spreadsheetId) {
      spreadsheetId = getEnv(`SPREADSHEET_ID_${location.toUpperCase()}`);

      if (!spreadsheetId) {
        return {
          success: false,
          message: `No spreadsheet ID found for location: ${location}`
        };
      }
    }

    console.log(`Deploying to ${location} (${spreadsheetId})...`);

    // Deploy to the specified location
    const result = deploySystem(location, spreadsheetId);

    console.log(`Deployment result: ${result.message}`);

    return result;
  } catch (error) {
    logError("deploy", error, { location, spreadsheetId });
    return {
      success: false,
      message: `Deployment failed: ${error.message || String(error)}`
    };
  }
}

/**
 * Deploys the Lab Cases Management System to all locations.
 *
 * @param {Object} locations - An object mapping location names to spreadsheet IDs.
 * @returns {Object} Deployment results for each location.
 */
function deployToAll(locations = null) {
  try {
    // If no locations provided, get from environment
    if (!locations) {
      locations = getSpreadsheetIds();

      if (Object.keys(locations).length === 0) {
        console.log('No locations configured. Please configure locations first.');
        return {
          success: false,
          message: 'No locations configured'
        };
      }
    }
  } catch (error) {
    logError("deployToAll", error, { message: "Error getting spreadsheet IDs" });
    return {
      success: false,
      message: `Failed to get locations: ${error.message || String(error)}`
    };
  }

  const results = {};
  let successCount = 0;
  let failureCount = 0;

  for (const location in locations) {
    const spreadsheetId = locations[location];
    console.log(`Deploying to ${location} (${spreadsheetId})...`);

    try {
      const result = deploySystem(location, spreadsheetId);
      results[location] = result;

      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }

      console.log(`Deployment to ${location} ${result.success ? 'succeeded' : 'failed'}: ${result.message}`);
    } catch (error) {
      logError("deployToAll", error, { location, spreadsheetId });
      results[location] = {
        success: false,
        message: `Deployment failed: ${error.message || String(error)}`
      };
      failureCount++;
    }
  }

  console.log(`Deployment completed with ${successCount} successes and ${failureCount} failures.`);

  return {
    success: successCount > 0 && failureCount === 0,
    message: `Deployment completed with ${successCount} successes and ${failureCount} failures`,
    results
  };
}

// Example usage:
// deployToAll(); // Uses environment variables
//
// Or with explicit locations:
// const locations = {
//   baytown: '1ToNL2R7v8No7Fsg9CuMBi92X2D1VGfCW4nmlHQ1R_sTv57ZNPqrtZ4KY',
//   humble: '1b5zqQ2TRjkLVR3XSZ8jODpRRyT_e2e5VWzWthR17_CLAgXQV4rvBYAeh'
// };
// deployToAll(locations);
