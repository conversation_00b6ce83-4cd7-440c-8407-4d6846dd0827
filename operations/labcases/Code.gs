/**
 * @fileoverview Main entry point for the Lab Cases Management System.
 * Provides global functions that can be called from the Google Sheets UI.
 */

/**
 * Handles edit events in the spreadsheet.
 * This function is automatically called by Google Sheets when a cell is edited.
 *
 * @param {Object} e - The edit event object.
 * @returns {void}
 */
function onEdit(e) {
  try {
    // Call the handler function from labcaseHandler
    return handleEdit(e);
  } catch (error) {
    console.error(`Error in onEdit: ${error.message || error}`);
    // Don't use logError here to avoid potential infinite recursion
  }
}

/**
 * Creates a custom menu when the spreadsheet is opened.
 * This function is automatically called by Google Sheets when the spreadsheet is opened.
 *
 * @returns {void}
 */
function onOpen() {
  try {
    // Call the createMenu function
    createMenu(SpreadsheetApp.getActiveSpreadsheet());
  } catch (error) {
    console.error(`Error in onOpen: ${error.message || error}`);
  }
}

/**
 * Runs a batch process to process all rows in the source sheet.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function runBatchProcess() {
  try {
    // Call the batch process function from labcaseHandler
    const result = processBatchRows({});

    // Show result to user
    const ui = SpreadsheetApp.getUi();
    if (result.success) {
      ui.alert(
        'Batch Process Complete',
        `Processed ${result.processedCount} out of ${result.totalRows} rows. ${result.errorCount} errors occurred.`,
        ui.ButtonSet.OK
      );
    } else {
      ui.alert(
        'Batch Process Failed',
        `Error: ${result.error}`,
        ui.ButtonSet.OK
      );
    }
  } catch (error) {
    logError("runBatchProcess", error);

    // Show error to user
    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }
  }
}

/**
 * Removes duplicate rows from all target sheets.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function removeDuplicateRows() {
  try {
    const result = cleanupDuplicateRows({});

    // Show result to user
    const ui = SpreadsheetApp.getUi();
    if (result.success) {
      ui.alert(
        'Duplicate Removal Complete',
        `Removed ${result.removedCount} duplicate rows.`,
        ui.ButtonSet.OK
      );
    } else {
      ui.alert(
        'Duplicate Removal Failed',
        `Error: ${result.error}`,
        ui.ButtonSet.OK
      );
    }
  } catch (error) {
    logError("removeDuplicateRows", error);

    // Show error to user
    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }
  }
}

/**
 * Sets up the Lab Cases Management System.
 * This function can be called from the custom menu.
 *
 * @param {string} [spreadsheetId=null] - Optional spreadsheet ID. If not provided, uses active spreadsheet.
 * @returns {Object} Setup result with success status and message.
 */
function setupLabCasesSystem(spreadsheetId = null) {
  try {
    const result = setupSystem(spreadsheetId);

    // Show result to user if UI is available
    try {
      const ui = SpreadsheetApp.getUi();
      ui.alert(
        'Setup Result',
        result.message,
        ui.ButtonSet.OK
      );
    } catch (e) {
      // UI not available, just log the result
      console.log(`Setup result: ${result.message}`);
    }

    return result;
  } catch (error) {
    logError("setupLabCasesSystem", error, { spreadsheetId });
    return {
      success: false,
      message: `Setup failed: ${error.message || String(error)}`
    };
  }
}

/**
 * Shows documentation for the Lab Cases Management System.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function showDocumentation() {
  try {
    showDocs();
  } catch (error) {
    logError("showDocumentation", error);
  }
}

/**
 * Deploys the Lab Cases Management System to the Baytown location.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function deployToBaytown() {
  try {
    const result = deploySystem('baytown');

    // Show result to user
    const ui = SpreadsheetApp.getUi();
    ui.alert(
      'Deployment Result',
      result.message,
      ui.ButtonSet.OK
    );
  } catch (error) {
    logError("deployToBaytown", error);

    // Show error to user
    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }
  }
}

/**
 * Deploys the Lab Cases Management System to the Humble location.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function deployToHumble() {
  try {
    const result = deploySystem('humble');

    // Show result to user
    const ui = SpreadsheetApp.getUi();
    ui.alert(
      'Deployment Result',
      result.message,
      ui.ButtonSet.OK
    );
  } catch (error) {
    logError("deployToHumble", error);

    // Show error to user
    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }
  }
}

/**
 * Configures a location with a spreadsheet ID.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function configureLocation() {
  try {
    configureEnv();
  } catch (error) {
    logError("configureLocation", error);
  }
}

/**
 * Lists all environment variables.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function listEnvironmentVars() {
  try {
    listEnv();
  } catch (error) {
    logError("listEnvironmentVars", error);
  }
}

/**
 * Deletes an environment variable.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function deleteEnvironmentVar() {
  try {
    deleteEnvVar();
  } catch (error) {
    logError("deleteEnvironmentVar", error);
  }
}

/**
 * Deploys the Lab Cases Management System to all configured locations.
 * This function can be called from the custom menu.
 *
 * @returns {void}
 */
function deployToAllLocations() {
  try {
    deployAll();
  } catch (error) {
    logError("deployToAllLocations", error);

    // Show error to user
    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }
  }
}

// In Google Apps Script, functions are automatically exposed globally
// No need to export them explicitly
