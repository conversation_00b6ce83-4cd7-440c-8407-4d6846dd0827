module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: ['eslint:recommended'],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'script',
  },
  rules: {
    'no-unused-vars': 'warn',
    'no-undef': 'warn',
  },
  globals: {
    // Google Apps Script globals
    SpreadsheetApp: 'readonly',
    PropertiesService: 'readonly',
    Logger: 'readonly',
    Session: 'readonly',
    console: 'readonly',
  },
};
