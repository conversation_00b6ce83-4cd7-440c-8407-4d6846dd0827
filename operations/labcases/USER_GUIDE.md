# Lab Cases Management System - User Guide

This guide provides step-by-step instructions for setting up and using the Lab Cases Management System for Kam Dental.

## Table of Contents

1. [Initial Setup](#initial-setup)
2. [Understanding the System](#understanding-the-system)
3. [Daily Usage](#daily-usage)
4. [Troubleshooting](#troubleshooting)
5. [Advanced Features](#advanced-features)
6. [Deployment to Multiple Locations](#deployment-to-multiple-locations)

## Initial Setup

### For Administrators

#### Option 1: Setting Up in a New Spreadsheet

1. **Create a New Google Sheet**
   - Go to [Google Sheets](https://sheets.google.com) and create a new spreadsheet
   - Name it appropriately (e.g., "Kam Dental Lab Cases - [Location]")

2. **Open the Apps Script Editor**
   - Click on **Extensions** > **Apps Script**
   - This will open the Google Apps Script editor in a new tab

3. **Deploy the Code**
   - If you're using the clasp CLI tool:
     ```
     cd operations/labcases
     pnpm run login
     pnpm run push
     ```
   - If you're manually copying code:
     - Create the following files in the Apps Script editor:
       - `Code.gs`
       - `labcasesConfig.gs`
       - `environment.gs`
       - `labcaseHandler.gs`
       - `sheetUtils.gs`
       - `columnUtils.gs`
       - `errorLogger.gs`
       - `setupService.gs`
       - `deploy.gs`
     - Copy the code from each file in this repository to the corresponding file in the Apps Script editor
     - Save all files

4. **Run the Setup Function**
   - In the Apps Script editor, select the `setupLabCasesSystem` function
   - Click the ▶️ (Run) button
   - When prompted, authorize the script to access your Google Sheets
   - You should see a success message when the setup is complete

5. **Refresh the Spreadsheet**
   - Go back to your Google Sheet and refresh the page
   - You should now see a new menu called "KamDental Lab Cases"
   - The system has created all necessary sheets with proper headers

### For End Users

If an administrator has already set up the system, you only need to:

1. **Open the Spreadsheet**
   - Open the Google Sheet that contains the Lab Cases Management System
   - You should see multiple sheets including "LAB CASES", "Completed Cases", etc.

2. **Run Initial Setup (if needed)**
   - If this is your first time using the system, click on **KamDental Lab Cases** > **Setup System**
   - This will ensure all necessary sheets and triggers are properly configured

## Understanding the System

### Sheet Structure

The system creates and manages the following sheets:

- **LAB CASES**: Main working sheet for active lab cases
- **Completed Cases**: Cases that have been completed
- **Attention Required**: Cases that need attention
- **Return**: Cases to be returned
- **Cancelled Cases**: Cases that have been cancelled
- **Re-do Cases**: Cases that need to be redone
- **Scheduled + Paid**: Cases that are scheduled and paid
- **Error Log**: System errors and issues (for troubleshooting)

### Column Structure

The system expects the following columns in the LAB CASES sheet:

1. **Patient Name (First, Last)**: Patient's full name
2. **INSURANCE STATUS**: Current insurance status (e.g., PAID, PENDING)
3. **APPOINTMENT STATUS**: Current appointment status (e.g., SCHEDULED, COMPLETED)
4. **Last Edited**: Timestamp of the last edit (automatically updated)
5. **CASE TYPE**: Type of lab case
6. **SEAT DATE**: Date when the case is scheduled to be seated
7. **Doctor**: Doctor assigned to the case (dropdown: DKAM, DOBI, DCHI)
8. **Total Fee**: Total fee for the case (numeric value)

### Status Values

The system recognizes the following status values:

#### Appointment Status
- **COMPLETED**: Moves the case to the "Completed Cases" sheet
- **ATTENTION NEEDED**: Copies the case to the "Attention Required" sheet
- **RE-DO**: Moves the case to the "Re-do Cases" sheet
- **CANCELLED**: Moves the case to the "Cancelled Cases" sheet
- **RETURN**: Moves the case to the "Return" sheet
- **SCHEDULED**: When combined with PAID insurance status, copies to "Scheduled + Paid" sheet

#### Insurance Status
- **PAID**: When combined with SCHEDULED appointment status, copies to "Scheduled + Paid" sheet

## Daily Usage

### Adding New Lab Cases

1. Go to the **LAB CASES** sheet
2. Enter the patient information in a new row
3. Fill in all required columns:
   - Patient Name (First, Last)
   - INSURANCE STATUS
   - APPOINTMENT STATUS
   - CASE TYPE
   - SEAT DATE
   - Doctor (select from dropdown)
   - Total Fee

### Updating Case Status

1. Go to the **LAB CASES** sheet
2. Find the case you want to update
3. Change the **APPOINTMENT STATUS** or **INSURANCE STATUS** column
4. The system will automatically:
   - Move the case to the appropriate sheet based on its status
   - Update the "Last Edited" timestamp

### Special Status Combinations

- If you set **APPOINTMENT STATUS** to "SCHEDULED" and **INSURANCE STATUS** to "PAID", the case will be copied to the "Scheduled + Paid" sheet while remaining in the LAB CASES sheet
- If you set **APPOINTMENT STATUS** to "ATTENTION NEEDED", the case will be copied to the "Attention Required" sheet while remaining in the LAB CASES sheet

### Running Batch Process

If you've made multiple changes or want to ensure all cases are in the correct sheets:

1. Click on **KamDental Lab Cases** > **Run Batch Process**
2. The system will process all rows in the LAB CASES sheet
3. You'll see a message showing how many rows were processed

### Removing Duplicate Rows

To clean up duplicate entries:

1. Click on **KamDental Lab Cases** > **Remove Duplicate Rows**
2. The system will identify and remove duplicate entries from all sheets
3. You'll see a message showing how many duplicates were removed

### Generating Monthly Fee Reports

To generate a report of fees by doctor:

1. Click on **KamDental Lab Cases** > **Generate Monthly Fees by Doctor**
2. Enter the month and year when prompted
3. The system will create a new sheet with the fee report

## Troubleshooting

### Common Issues

#### Case Not Moving to Expected Sheet

1. Check that the **APPOINTMENT STATUS** is spelled correctly and matches one of the recognized values
2. Run the batch process manually: **KamDental Lab Cases** > **Run Batch Process**
3. Check the Error Log sheet for any errors related to the case

#### Missing Menu

If you don't see the "KamDental Lab Cases" menu:

1. Refresh the page
2. If the menu still doesn't appear, go to **Extensions** > **Apps Script**
3. Run the `onOpen` function manually
4. Return to the spreadsheet and check for the menu

#### Error Messages

If you encounter an error message:

1. Note the exact error message
2. Check the Error Log sheet for more details
3. If needed, run **KamDental Lab Cases** > **Setup System** to reset the system

## Advanced Features

### Environment Configuration

The system can be configured to work with multiple locations:

1. Click on **KamDental Lab Cases** > **Environment** > **Configure Location**
2. Enter the location name (e.g., "baytown", "humble")
3. Enter the spreadsheet ID for that location
4. The system will save this configuration for future deployments

To view all configured locations:

1. Click on **KamDental Lab Cases** > **Environment** > **List Environment Variables**
2. You'll see a list of all configured locations and their spreadsheet IDs

To delete a configuration:

1. Click on **KamDental Lab Cases** > **Environment** > **Delete Environment Variable**
2. Enter the name of the variable to delete (e.g., "SPREADSHEET_ID_BAYTOWN")

### Customizing the System

Advanced users can customize the system by modifying the configuration in `labcasesConfig.gs`. You can customize:

- Sheet names
- Column headers
- Status values
- Header row position
- Allowed doctor values

## Deployment to Multiple Locations

### Deploying to a Specific Location

1. Click on **KamDental Lab Cases** > **Deployment** > **Deploy to Baytown** or **Deploy to Humble**
2. If you haven't configured the location yet, you'll be prompted to enter the spreadsheet ID
3. The system will be deployed to the specified location

### Deploying to All Locations

1. Configure all locations using the Environment menu
2. Click on **KamDental Lab Cases** > **Deployment** > **Deploy to All Locations**
3. The system will be deployed to all configured locations

### Finding Your Spreadsheet ID

To find the spreadsheet ID:

1. Open the Google Sheet
2. Look at the URL in your browser
3. The spreadsheet ID is the long string of characters between `/d/` and `/edit`
   - Example: `https://docs.google.com/spreadsheets/d/`**`1ToNL2R7v8No7Fsg9CuMBi92X2D1VGfCW4nmlHQ1R_sTv57ZNPqrtZ4KY`**`/edit`

## Getting Help

If you need assistance with the Lab Cases Management System:

1. Click on **KamDental Lab Cases** > **Show Documentation** for a quick overview
2. Refer to this User Guide for detailed instructions
3. Check the Error Log sheet for any system errors
4. Contact Kam Dental support for additional help
