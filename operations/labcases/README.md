# Lab Cases Management System

A modular Google Apps Script for managing dental lab cases with easy setup and robust error logging.

## Features

- **Modular Structure**: Clean separation of concerns for better maintainability
- **Easy Setup**: Configure the system with a single function call
- **Automatic Sheet Creation**: Creates all necessary sheets with proper headers
- **Status-Based Organization**: Automatically moves cases between sheets based on their status
- **Duplicate Detection**: Identifies and removes duplicate entries
- **Robust Error Logging**: Comprehensive error tracking and reporting
- **Configurable**: Customizable sheet names, column mappings, and status values

## Directory Structure

```
operations/
└── labcases/
    ├── config/
    │   ├── labcasesConfig.js       # Centralized configuration
    │   └── environment.js          # Environment variables management
    ├── services/
    │   ├── labcaseHandler.js       # Core functionality for processing lab cases
    │   └── setupService.js         # Setup and initialization
    ├── utils/
    │   ├── columnUtils.js          # Column-related utilities
    │   ├── sheetUtils.js           # Sheet management utilities
    │   └── errorLogger.js          # Error logging and handling
    ├── Code.js                     # Main entry point
    └── deploy.js                   # Deployment utilities
```

## Setup Instructions

1. Open your Google Sheets document
2. Go to Extensions > Apps Script
3. Copy the code from each file in this repository to the corresponding file in the Apps Script editor
4. Save all files
5. Run the `setupLabCasesSystem` function to set up the system

## Sheet Structure

The system creates and manages the following sheets:

- **LAB CASES**: Main working sheet for active lab cases
- **Completed Cases**: Cases that have been completed
- **Attention Required**: Cases that need attention
- **Return**: Cases to be returned
- **Cancelled Cases**: Cases that have been cancelled
- **Re-do Cases**: Cases that need to be redone
- **Scheduled + Paid**: Cases that are scheduled and paid
- **Error Log**: System errors and issues

## Column Structure

The system expects the following columns:

1. Patient Name (First, Last)
2. INSURANCE STATUS
3. APPOINTMENT STATUS
4. Last Edited
5. CASE TYPE
6. SEAT DATE
7. Doctor (dropdown: DKAM, DOBI, DCHI)
8. Total Fee (numeric)

## Workflow

1. Add new lab cases to the LAB CASES sheet
2. Update the APPOINTMENT STATUS and INSURANCE STATUS columns
3. The system will automatically move cases to the appropriate sheet based on their status
4. Cases with ATTENTION NEEDED status will be copied to the Attention Required sheet
5. Cases with SCHEDULED status and PAID insurance status will be copied to the Scheduled + Paid sheet

## API Reference

### Global Functions
- `setupLabCasesSystem(spreadsheetId)`: Sets up the system in the specified spreadsheet
- `runBatchProcess()`: Processes all rows in the LAB CASES sheet
- `removeDuplicateRows()`: Removes duplicate entries from all sheets
- `showDocumentation()`: Displays system documentation
- `generateDoctorFeeReport(month, year)`: Generates a monthly fee report by doctor
- `generateDoctorFeeReportMenu()`: Prompts for month/year and runs the doctor fee report

### Deployment Functions

- `deployToBaytown()`: Deploys the system to the Baytown location
- `deployToHumble()`: Deploys the system to the Humble location
- `deployToAllLocations()`: Deploys the system to all configured locations

### Environment Configuration Functions

- `configureLocation()`: Configures a location with a spreadsheet ID
- `listEnvironmentVars()`: Lists all environment variables
- `deleteEnvironmentVar()`: Deletes an environment variable

### Configuration

The system can be customized by modifying the configuration in `config/labcasesConfig.js`. The following settings can be customized:

- Sheet names
- Column headers
- Status values
- Header row position

### Environment Variables

The system uses environment variables to store spreadsheet IDs for different locations. These can be configured through the UI or programmatically:

1. **Through the UI**: Use the "Environment > Configure Location" menu option
2. **Programmatically**: Use the `setEnv()` function in `config/environment.js`

Environment variables are stored using Google Apps Script's Properties Service, making them persistent across script executions.

## Error Handling

The system includes comprehensive error handling with:

- Custom error classes for different types of errors
- Detailed error logging to the Error Log sheet
- Function wrapping for consistent error handling
- User-friendly error messages

## Deployment

There are multiple ways to deploy the system:

### Option 1: Deploy to a Specific Location with UI Prompt

1. Use the `deployToBaytown()` or `deployToHumble()` function
2. If no spreadsheet ID is configured, you'll be prompted to enter one
3. The system will be set up in the specified spreadsheet

### Option 2: Deploy to a Specific Location with Environment Variables

1. Configure the location using "Environment > Configure Location"
2. Use the `deployToBaytown()` or `deployToHumble()` function
3. The system will use the stored spreadsheet ID

### Option 3: Deploy to All Configured Locations

1. Configure multiple locations using "Environment > Configure Location"
2. Use the "Deployment > Deploy to All Locations" menu option
3. The system will be deployed to all configured locations

## Development

To contribute to the development of this system:

1. Clone the repository
2. Make your changes
3. Test your changes using the Google Apps Script editor
4. Submit a pull request
