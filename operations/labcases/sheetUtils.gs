/**
 * @fileoverview Utilities for sheet management in the Lab Cases Management System.
 * Provides functions for creating, accessing, and manipulating sheets.
 */

/**
 * Gets all required sheets for the Lab Cases Management System.
 * Creates any missing sheets with proper headers.
 *
 * @param {Object} ss - The spreadsheet object.
 * @param {Object} config - The configuration object.
 * @returns {Object} An object mapping sheet names to sheet objects.
 */
function getOrCreateSheets(ss, config) {
  try {
    const sheets = {};
    const sheetNames = config.SHEET_NAMES;

    // Get or create each sheet
    for (const key in sheetNames) {
      const sheetName = sheetNames[key];
      let sheet = ss.getSheetByName(sheetName);

      if (!sheet) {
        // Create the sheet if it doesn't exist
        sheet = ss.insertSheet(sheetName);

        // Add headers to the new sheet
        if (key === 'SOURCE' || key === 'ERROR_LOG') {
          // Special handling for source sheet and error log
          const headers = key === 'SOURCE' ?
            Object.values(config.COLUMN_HEADERS) :
            config.ERROR_LOG_HEADERS;

          // Ensure all headers are included for SOURCE sheet
          if (key === 'SOURCE') {
            // Make sure we have all the expected headers
            const expectedHeaders = Object.values(config.COLUMN_HEADERS);
            // Check if all expected headers are in the headers array
            const missingHeaders = expectedHeaders.filter(header => !headers.includes(header));

            if (missingHeaders.length > 0) {
              console.log(`Adding missing headers: ${missingHeaders.join(', ')}`);
              // Add missing headers
              headers.push(...missingHeaders);
            }
          }

          sheet.appendRow(headers);
          sheet.getRange(1, 1, 1, headers.length).setFontWeight("bold");
        } else {
          // For other sheets, use the same headers as the source sheet
          const sourceSheet = ss.getSheetByName(sheetNames.SOURCE);
          if (sourceSheet) {
            const headerRow = config.HEADER_ROW || 1;
            const lastColumn = sourceSheet.getLastColumn();
            const headerValues = sourceSheet.getRange(headerRow, 1, 1, lastColumn).getValues()[0];

            sheet.appendRow(headerValues);
            sheet.getRange(1, 1, 1, headerValues.length).setFontWeight("bold");
          } else {
            // Fallback to default headers if source sheet doesn't exist
            const headers = Object.values(config.COLUMN_HEADERS);
            sheet.appendRow(headers);
            sheet.getRange(1, 1, 1, headers.length).setFontWeight("bold");
          }
        }
      }

      sheets[key] = sheet;
    }

    return sheets;
  } catch (error) {
    logError("getOrCreateSheets", error, { spreadsheetId: ss.getId() });
    throw new SheetOperationError("Failed to get or create sheets", { error: error.message });
  }
}

/**
 * Gets a sheet by name from the spreadsheet.
 *
 * @param {Object} ss - The spreadsheet object.
 * @param {string} sheetName - The name of the sheet to get.
 * @returns {Object} The sheet object.
 * @throws {NotFoundError} If the sheet is not found.
 */
function getSheetByName(ss, sheetName) {
  const sheet = ss.getSheetByName(sheetName);
  if (!sheet) {
    throw new NotFoundError(`Sheet not found: ${sheetName}`);
  }
  return sheet;
}

/**
 * Gets the header row from a sheet.
 *
 * @param {Object} sheet - The sheet object.
 * @param {number} headerRow - The row number where headers are located.
 * @returns {Array} The header values.
 */
function getHeaderRow(sheet, headerRow = 1) {
  try {
    const lastColumn = sheet.getLastColumn();
    if (lastColumn === 0) return [];

    return sheet.getRange(headerRow, 1, 1, lastColumn).getValues()[0];
  } catch (error) {
    logError("getHeaderRow", error, { sheetName: sheet.getName() });
    throw new SheetOperationError("Failed to get header row", { error: error.message });
  }
}

/**
 * Moves a row from one sheet to another.
 *
 * @param {Object} sourceSheet - The source sheet.
 * @param {Object} targetSheet - The target sheet.
 * @param {number} sourceRow - The row number in the source sheet.
 * @param {Array} rowData - The row data to append to the target sheet.
 * @param {boolean} deleteFromSource - Whether to delete the row from the source sheet.
 * @returns {boolean} Whether the operation was successful.
 */
function moveRow(sourceSheet, targetSheet, sourceRow, rowData, deleteFromSource = true) {
  try {
    // Append to target sheet
    targetSheet.appendRow(rowData);

    // Delete from source sheet if requested
    if (deleteFromSource) {
      sourceSheet.deleteRow(sourceRow);
    }

    return true;
  } catch (error) {
    logError("moveRow", error, {
      sourceSheet: sourceSheet.getName(),
      targetSheet: targetSheet.getName(),
      sourceRow
    });
    throw new SheetOperationError("Failed to move row", { error: error.message });
  }
}

/**
 * Checks if a row is a duplicate in the target sheet.
 *
 * @param {Object} sheet - The sheet to check for duplicates.
 * @param {Array} rowData - The row data to check.
 * @param {Array} keyColumnIndices - The indices of columns to use as keys for duplicate checking.
 * @param {number} headerRow - The row number where headers are located.
 * @returns {boolean} Whether the row is a duplicate.
 */
function isDuplicateRow(sheet, rowData, keyColumnIndices, headerRow = 1) {
  try {
    const lastRow = sheet.getLastRow();
    if (lastRow <= headerRow) return false; // No data rows yet

    const lastColumn = sheet.getLastColumn();
    const data = sheet.getRange(headerRow + 1, 1, lastRow - headerRow, lastColumn).getValues();

    // Check each row for a match on key columns
    return data.some(row => {
      return keyColumnIndices.every(index => {
        // Skip if index is out of bounds
        if (index >= row.length || index >= rowData.length) return false;

        const existingValue = row[index];
        const newValue = rowData[index];

        // Handle different data types
        if (existingValue instanceof Date && newValue instanceof Date) {
          return existingValue.getTime() === newValue.getTime();
        }

        return existingValue === newValue;
      });
    });
  } catch (error) {
    logError("isDuplicateRow", error, { sheetName: sheet.getName() });
    // Return false on error to allow the operation to proceed
    return false;
  }
}
