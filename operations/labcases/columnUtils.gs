/**
 * @fileoverview Utilities for column management in the Lab Cases Management System.
 * Provides functions for identifying columns by header names and managing column data.
 */

// Globals: logError, ValidationError are provided by errorLogger.gs

/**
 * Gets the column indices for all headers in the configuration.
 * 
 * @param {Object} sheet - The sheet object.
 * @param {Object} columnHeaders - The column headers configuration.
 * @param {number} headerRow - The row number where headers are located.
 * @returns {Object} An object mapping header keys to column indices.
 */
function getColumnIndices(sheet, columnHeaders, headerRow = 1) {
  try {
    const indices = {};
    const headerValues = sheet.getRange(headerRow, 1, 1, sheet.getLastColumn()).getValues()[0];
    
    // Find the index for each header
    for (const key in columnHeaders) {
      const headerName = columnHeaders[key];
      const index = headerValues.findIndex(header => 
        header && header.toString().trim() === headerName
      );
      
      indices[key] = index !== -1 ? index + 1 : null; // Convert to 1-based index
    }
    
    return indices;
  } catch (error) {
    logError("getColumnIndices", error, { sheetName: sheet.getName() });
    throw error;
  }
}

/**
 * Gets the column index for a specific header.
 * 
 * @param {Object} sheet - The sheet object.
 * @param {string} headerName - The header name to find.
 * @param {number} headerRow - The row number where headers are located.
 * @returns {number|null} The column index (1-based) or null if not found.
 */
function getColumnIndexByHeader(sheet, headerName, headerRow = 1) {
  try {
    const headerValues = sheet.getRange(headerRow, 1, 1, sheet.getLastColumn()).getValues()[0];
    const index = headerValues.findIndex(header => 
      header && header.toString().trim() === headerName
    );
    
    return index !== -1 ? index + 1 : null; // Convert to 1-based index
  } catch (error) {
    logError("getColumnIndexByHeader", error, { sheetName: sheet.getName(), headerName });
    return null;
  }
}

/**
 * Updates the "Last Edited" timestamp for a row.
 * 
 * @param {Object} sheet - The sheet object.
 * @param {number} row - The row number to update.
 * @param {number} timestampCol - The column index for the timestamp.
 * @returns {boolean} Whether the update was successful.
 */
function updateLastEditedTimestamp(sheet, row, timestampCol) {
  try {
    if (!timestampCol) return false;
    
    sheet.getRange(row, timestampCol).setValue(new Date());
    return true;
  } catch (error) {
    logError("updateLastEditedTimestamp", error, { sheetName: sheet.getName(), row, timestampCol });
    return false;
  }
}

/**
 * Capitalizes the first letter of each word in a string.
 * 
 * @param {string} str - The string to capitalize.
 * @returns {string} The capitalized string.
 */
function capitalizeWords(str) {
  if (!str || typeof str !== 'string') {
    return '';
  }
  
  return str.toLowerCase().replace(/\b\w/g, c => c.toUpperCase());
}

/**
 * Formats a patient name by capitalizing words.
 * 
 * @param {Object} sheet - The sheet object.
 * @param {number} row - The row number.
 * @param {number} patientNameCol - The column index for the patient name.
 * @returns {boolean} Whether the update was successful.
 */
function formatPatientName(sheet, row, patientNameCol) {
  try {
    if (!patientNameCol) return false;
    
    const range = sheet.getRange(row, patientNameCol);
    const value = range.getValue();
    
    if (typeof value === "string" && value.trim() !== "") {
      range.setValue(capitalizeWords(value));
      return true;
    }
    
    return false;
  } catch (error) {
    logError("formatPatientName", error, { sheetName: sheet.getName(), row, patientNameCol });
    return false;
  }
}

/**
 * Validates that required columns exist in the sheet.
 * 
 * @param {Object} columnIndices - The column indices object.
 * @param {Array} requiredColumns - The keys of required columns.
 * @returns {boolean} Whether all required columns exist.
 * @throws {ValidationError} If any required column is missing.
 */
function validateRequiredColumns(columnIndices, requiredColumns) {
  const missingColumns = [];
  
  for (const column of requiredColumns) {
    if (!columnIndices[column]) {
      missingColumns.push(column);
    }
  }
  
  if (missingColumns.length > 0) {
    throw new ValidationError(`Missing required columns: ${missingColumns.join(', ')}`);
  }
  
  return true;
}
