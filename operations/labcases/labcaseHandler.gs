/**
 * @fileoverview Core handler for lab case management operations.
 * Processes edit events and manages row movements based on status changes.
 */

/**
 * Processes a row in the lab cases sheet based on its status.
 *
 * @param {Object} sheet - The sheet containing the row.
 * @param {number} row - The row number to process.
 * @param {Object} config - The configuration object.
 * @returns {boolean} Whether the row was processed successfully.
 */
function processRow(sheet, row, config) {
  try {
    // Get column indices
    const columnIndices = getColumnIndices(sheet, config.COLUMN_HEADERS, config.HEADER_ROW);

    // Validate required columns
    validateRequiredColumns(columnIndices, ['APPOINTMENT_STATUS', 'INSURANCE_STATUS']);

    // Get the row data
    const rowData = sheet.getRange(row, 1, 1, sheet.getLastColumn()).getValues()[0];

    // Validate Doctor and Total Fee values
    const doctorCol = columnIndices.DOCTOR;
    const feeCol = columnIndices.TOTAL_FEE;
    if (doctorCol && feeCol) {
      try {
        const doctorRaw = rowData[doctorCol - 1];
        const feeRaw = rowData[feeCol - 1];
        const doctor = doctorRaw && doctorRaw.toString().trim();
        if (doctor && !config.ALLOWED_DOCTORS.includes(doctor)) {
          throw new Error(`Invalid doctor: ${doctor}`);
        }
        const fee = parseFloat(feeRaw);
        if (feeRaw !== '' && isNaN(fee)) {
          throw new Error(`Invalid total fee: ${feeRaw}`);
        }
      } catch (valError) {
        logError("processRow", valError, { sheetName: sheet.getName(), row });
      }
    }
    // Get status values
    const appointmentStatusCol = columnIndices.APPOINTMENT_STATUS;
    const insuranceStatusCol = columnIndices.INSURANCE_STATUS;
    const lastEditedCol = columnIndices.LAST_EDITED;

    const appointmentStatusRaw = rowData[appointmentStatusCol - 1];
    const insuranceStatusRaw = rowData[insuranceStatusCol - 1];
    const appointmentStatus = appointmentStatusRaw.toString().trim().toUpperCase();
    const insuranceStatus = insuranceStatusRaw.toString().trim().toUpperCase();

    console.log(`Processing row ${row}: Appointment Status="${appointmentStatus}", Insurance Status="${insuranceStatus}"`);

    // Get the spreadsheet
    const ss = sheet.getParent();

    // Check the "Scheduled + Paid" condition first
    if (appointmentStatus === config.STATUS_VALUES.APPOINTMENT.SCHEDULED &&
        insuranceStatus === config.STATUS_VALUES.INSURANCE.PAID) {
      console.log(`Row ${row}: SCHEDULED + PAID condition met.`);

      const targetSheet = getSheetByName(ss, config.SHEET_NAMES.SCHEDULED_PAID);
      moveRow(sheet, targetSheet, row, rowData, false); // Copy without deletion

      // Update timestamp
      if (lastEditedCol) {
        updateLastEditedTimestamp(sheet, row, lastEditedCol);
      }

      return true;
    }

    // Process based on appointment status
    let targetSheetName = null;
    let shouldDelete = true;

    switch (appointmentStatus) {
      case config.STATUS_VALUES.APPOINTMENT.COMPLETED:
        console.log(`Row ${row}: COMPLETED status met.`);
        targetSheetName = config.SHEET_NAMES.COMPLETED;
        break;
      case config.STATUS_VALUES.APPOINTMENT.REDO:
        console.log(`Row ${row}: RE-DO status met.`);
        targetSheetName = config.SHEET_NAMES.REDO;
        break;
      case config.STATUS_VALUES.APPOINTMENT.CANCELLED:
        console.log(`Row ${row}: CANCELLED status met.`);
        targetSheetName = config.SHEET_NAMES.CANCELLED;
        break;
      case config.STATUS_VALUES.APPOINTMENT.ATTENTION:
        console.log(`Row ${row}: ATTENTION NEEDED status met.`);
        targetSheetName = config.SHEET_NAMES.ATTENTION;
        shouldDelete = false; // Copy without deletion for Attention Required
        break;
      case config.STATUS_VALUES.APPOINTMENT.RETURN:
        console.log(`Row ${row}: RETURN status met.`);
        targetSheetName = config.SHEET_NAMES.RETURN;
        break;
      default:
        console.log(`Row ${row}: No matching status for processing.`);
        // Update timestamp if no status match
        if (lastEditedCol) {
          updateLastEditedTimestamp(sheet, row, lastEditedCol);
        }
        return false;
    }

    // Move the row if a target sheet was identified
    if (targetSheetName) {
      const targetSheet = getSheetByName(ss, targetSheetName);
      moveRow(sheet, targetSheet, row, rowData, shouldDelete);

      // Update timestamp if not deleting
      if (!shouldDelete && lastEditedCol) {
        updateLastEditedTimestamp(sheet, row, lastEditedCol);
      }
    }

    return true;
  } catch (error) {
    logError("processRow", error, { sheetName: sheet.getName(), row });
    return false;
  }
}

/**
 * Handles edit events in the spreadsheet.
 *
 * @param {Object} e - The edit event object.
 * @param {Object} customConfig - Optional custom configuration.
 * @returns {boolean} Whether the edit was handled successfully.
 */
function handleEdit(e, customConfig = {}) {
  try {
    if (!e || !e.range) {
      console.log('Invalid edit event.');
      return false;
    }

    const sheet = e.range.getSheet();
    const config = getLabCasesConfig(customConfig);

    // Check if this is the source sheet
    if (sheet.getName() !== config.SHEET_NAMES.SOURCE) {
      console.log(`Edit in "${sheet.getName()}" ignored.`);
      return false;
    }

    const row = e.range.getRow();

    // Ignore edits in the header row
    if (row <= config.HEADER_ROW) {
      console.log(`Edit in header row ignored.`);
      return false;
    }

    // Format patient name if that column was edited
    const patientNameCol = getColumnIndexByHeader(sheet, config.COLUMN_HEADERS.PATIENT_NAME, config.HEADER_ROW);
    if (e.range.getColumn() === patientNameCol) {
      formatPatientName(sheet, row, patientNameCol);
    }

    // Process the row
    console.log(`onEdit triggered for row ${row}.`);
    return processRow(sheet, row, config);
  } catch (error) {
    logError("handleEdit", error, { event: JSON.stringify(e) });
    return false;
  }
}

/**
 * Runs a batch process to process all rows in the source sheet.
 *
 * @param {Object} customConfig - Optional custom configuration.
 * @returns {Object} Result of the batch process.
 */
function processBatchRows(customConfig = {}) {
  try {
    console.log('Batch process started.');

    const config = getLabCasesConfig(customConfig);
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = getSheetByName(ss, config.SHEET_NAMES.SOURCE);

    const lastRow = sheet.getLastRow();
    console.log(`Total rows to process: ${lastRow - config.HEADER_ROW}.`);

    let processedCount = 0;
    let errorCount = 0;

    // Process rows from bottom up to avoid issues with row deletions
    for (let row = lastRow; row > config.HEADER_ROW; row--) {
      try {
        const result = processRow(sheet, row, config);
        if (result) {
          processedCount++;
        }
      } catch (rowError) {
        errorCount++;
        logError("runBatchProcess", rowError, { row });
      }
    }

    console.log('Batch process completed.');

    return {
      success: true,
      processedCount,
      errorCount,
      totalRows: lastRow - config.HEADER_ROW
    };
  } catch (error) {
    logError("runBatchProcess", error);
    return {
      success: false,
      error: error.message || String(error)
    };
  }
}

/**
 * Removes duplicate rows from all target sheets.
 *
 * @param {Object} customConfig - Optional custom configuration.
 * @returns {Object} Result of the duplicate removal process.
 */
function cleanupDuplicateRows(customConfig = {}) {
  try {
    console.log('Removing duplicate rows from all sheets.');

    const config = getLabCasesConfig(customConfig);
    const ss = SpreadsheetApp.getActiveSpreadsheet();

    // Get key column indices for duplicate checking
    const sourceSheet = getSheetByName(ss, config.SHEET_NAMES.SOURCE);
    const columnIndices = getColumnIndices(sourceSheet, config.COLUMN_HEADERS, config.HEADER_ROW);

    // Define key columns for duplicate checking
    const keyColumns = ['PATIENT_NAME', 'CASE_TYPE', 'SEAT_DATE'];
    const keyColumnIndices = keyColumns
      .map(key => columnIndices[key] ? columnIndices[key] - 1 : null) // Convert to 0-based
      .filter(index => index !== null);

    if (keyColumnIndices.length === 0) {
      throw new Error('No key columns found for duplicate checking');
    }

    let removedCount = 0;

    // Check each target sheet
    for (const key in config.SHEET_NAMES) {
      if (key === 'SOURCE') continue; // Skip source sheet

      const sheetName = config.SHEET_NAMES[key];
      const sheet = ss.getSheetByName(sheetName);

      if (!sheet) continue;

      const lastRow = sheet.getLastRow();
      if (lastRow <= config.HEADER_ROW) continue; // No data rows

      const lastColumn = sheet.getLastColumn();
      const data = sheet.getRange(config.HEADER_ROW + 1, 1, lastRow - config.HEADER_ROW, lastColumn).getValues();

      // Track rows to delete (in reverse order to avoid shifting issues)
      const rowsToDelete = [];

      // Check each row against all previous rows
      for (let i = 0; i < data.length; i++) {
        const currentRow = data[i];

        // Check if this row is a duplicate of any previous row
        for (let j = 0; j < i; j++) {
          const previousRow = data[j];

          // Check if key columns match
          const isDuplicate = keyColumnIndices.every(index => {
            if (index >= currentRow.length || index >= previousRow.length) return false;

            const currentValue = currentRow[index];
            const previousValue = previousRow[index];

            // Handle different data types
            if (currentValue instanceof Date && previousValue instanceof Date) {
              return currentValue.getTime() === previousValue.getTime();
            }

            return currentValue === previousValue;
          });

          if (isDuplicate) {
            rowsToDelete.push(config.HEADER_ROW + 1 + i);
            break;
          }
        }
      }

      // Delete duplicate rows (in reverse order)
      for (let i = rowsToDelete.length - 1; i >= 0; i--) {
        sheet.deleteRow(rowsToDelete[i]);
        removedCount++;
      }
    }

    console.log(`Removed ${removedCount} duplicate rows.`);

    return {
      success: true,
      removedCount
    };
  } catch (error) {
    logError("removeDuplicateRows", error);
    return {
      success: false,
      error: error.message || String(error)
    };
  }
}

// Make functions available globally
// No need to export them explicitly in Google Apps Script
