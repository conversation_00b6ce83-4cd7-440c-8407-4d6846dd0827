/**
 * @fileoverview Environment configuration for the Lab Cases Management System.
 * Provides access to environment-specific variables.
 */

// Spreadsheet IDs for different locations
const SPREADSHEET_IDS = {
  BAYTOWN: '1ToNL2R7v8No7Fsg9CuMBi92X2D1VGfCW4nmlHQ1R_sTv57ZNPqrtZ4KY',
  HUMBLE: '1b5zqQ2TRjkLVR3XSZ8jODpRRyT_e2e5VWzWthR17_CLAgXQV4rvBYAeh'
};

/**
 * Gets an environment variable.
 * First checks script properties, then falls back to predefined constants.
 *
 * @param {string} key - The environment variable key.
 * @returns {string|null} The environment variable value or null if not found.
 */
function getEnv(key) {
  try {
    // Check if key is defined
    if (!key) {
      console.error('Error: Undefined key passed to getEnv');
      return null;
    }

    // Try to get from script properties first (allows runtime configuration)
    const scriptProperties = PropertiesService.getScriptProperties();
    const value = scriptProperties.getProperty(key);

    if (value) {
      return value;
    }

    // Fall back to predefined constants
    if (key.startsWith('SPREADSHEET_ID_')) {
      const location = key.replace('SPREADSHEET_ID_', '');
      return SPREADSHEET_IDS[location] || null;
    }

    return null;
  } catch (error) {
    console.error(`Error getting environment variable ${key}: ${error.message}`);
    return null;
  }
}

/**
 * Sets an environment variable in script properties.
 *
 * @param {string} key - The environment variable key.
 * @param {string} value - The environment variable value.
 * @returns {boolean} Whether the operation was successful.
 */
function setEnv(key, value) {
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    scriptProperties.setProperty(key, value);
    return true;
  } catch (error) {
    console.error(`Error setting environment variable ${key}: ${error.message}`);
    return false;
  }
}

/**
 * Gets all spreadsheet IDs for deployment.
 *
 * @returns {Object} An object mapping location names to spreadsheet IDs.
 */
function getSpreadsheetIds() {
  const ids = {};

  // Try to get from script properties first
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    const properties = scriptProperties.getProperties();

    for (const key in properties) {
      if (key.startsWith('SPREADSHEET_ID_')) {
        const location = key.replace('SPREADSHEET_ID_', '').toLowerCase();
        ids[location] = properties[key];
      }
    }
  } catch (error) {
    console.error(`Error getting spreadsheet IDs from properties: ${error.message}`);
  }

  // Fall back to predefined constants for any missing locations
  if (!ids.baytown && SPREADSHEET_IDS.BAYTOWN) {
    ids.baytown = SPREADSHEET_IDS.BAYTOWN;
  }

  if (!ids.humble && SPREADSHEET_IDS.HUMBLE) {
    ids.humble = SPREADSHEET_IDS.HUMBLE;
  }

  return ids;
}

/**
 * Lists all environment variables.
 *
 * @returns {Object} An object containing all environment variables.
 */
function listEnvVars() {
  const vars = {};

  // Get from script properties
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    const properties = scriptProperties.getProperties();

    for (const key in properties) {
      vars[key] = properties[key];
    }
  } catch (error) {
    console.error(`Error listing environment variables: ${error.message}`);
  }

  // Add predefined constants if not overridden
  for (const location in SPREADSHEET_IDS) {
    const key = `SPREADSHEET_ID_${location}`;
    if (!vars[key]) {
      vars[key] = SPREADSHEET_IDS[location];
    }
  }

  return vars;
}

/**
 * Deletes an environment variable.
 *
 * @param {string} key - The environment variable key to delete.
 * @returns {boolean} Whether the operation was successful.
 */
function deleteEnv(key) {
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    scriptProperties.deleteProperty(key);
    return true;
  } catch (error) {
    console.error(`Error deleting environment variable ${key}: ${error.message}`);
    return false;
  }
}
