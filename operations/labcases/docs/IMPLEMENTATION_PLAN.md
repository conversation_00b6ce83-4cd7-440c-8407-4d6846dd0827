# Implementation Plan

This document breaks down the development tasks to implement the features defined in the PRD.

1. Header Mapping Utility
   - Extend `utils/columnUtils.gs` (or create `utils/headerUtils.js`) with a function `getHeaderMap(sheet)` that reads the header row and returns a map `{ headerName: columnIndex }`.
   - Replace all static column-index references in services (`labcaseHandler.js`, `cleanupDuplicates`, etc.) with lookups via `getHeaderMap`.

2. Configuration Updates
   - In `config/labcasesConfig.js`:
     * Add `COLUMN_HEADERS.DOCTOR = 'Doctor'` and `COLUMN_HEADERS.TOTAL_FEE = 'Total Fee'`.
     * Add a new property `ALLOWED_DOCTORS = ['DKAM', 'DOBI', 'DCHI']`.
     * Ensure `COLUMN_INDICES.DOCTOR` and `COLUMN_INDICES.TOTAL_FEE` default to `null` and are populated at runtime.
   - Update `getLabCasesConfig()` to merge in `ALLOWED_DOCTORS`.

3. Setup Service Enhancements
   - In `services/setupService.js`, update the setup routine to:
     * Create or add missing headers for Doctor and Total Fee in the source sheet header row.
     * After sheet creation, apply a data-validation rule (dropdown) on the Doctor column using `ALLOWED_DOCTORS`.

4. Data Processing Updates
   - In `services/labcaseHandler.js` (and `batchProcess`):
     * When reading rows, include Doctor and Total Fee columns via the header map.
     * Validate that `Total Fee` is numeric; if not, use `logError`.
     * Ensure row-movement logic carries new columns correctly.

5. Aggregation Service
   - Create a new service `services/reportService.js` with function `generateDoctorFeeReport(month, year)`:
     * Read all rows from the completed cases sheet (or main sheet by status).
     * Use header map to extract `Doctor`, `Total Fee`, and date (e.g. `SEAT_DATE`).
     * Aggregate fees per doctor for the specified month/year.
     * Write results to a new or existing sheet named `Doctor Fee Summary`, with columns `[Month, Doctor, Total Fee]`.

6. Menu Integration & Triggers
   - In `services/setupService.js`, add a menu item “Generate Monthly Fees by Doctor” bound to a global function `generateDoctorFeeReportMenu()` that prompts for month/year and calls `generateDoctorFeeReport`.
   - Optionally use `ScriptApp.newTrigger()` in code or manifest to schedule the report on the 1st of each month.

7. Testing & Documentation
   - Perform manual testing in a sample spreadsheet to verify:
     * Dropdown validation for Doctor
     * Correct reading and validation of Total Fee
     * Aggregation output accuracy
   - Update `README.md` and inline JSDoc comments to reflect new fields and menu options.

8. Deployment
   - Use existing deployment functions (`deployToBaytown`, `deployToHumble`, or `deployToAllLocations`) to push updates to live spreadsheets.

---
*Implementation plan saved to `operations/labcases/IMPLEMENTATION_PLAN.md`*