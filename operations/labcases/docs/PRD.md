# Lab Cases Management System – Product Requirements Document (PRD)

## 1. Purpose
This document captures the existing capabilities of the Lab Cases Management System and outlines proposed enhancements to support dynamic header lookup, doctor and fee tracking, and automated monthly fee reports per doctor.

## 2. Current System Capabilities
- Modular Apps Script deployed to Google Sheets with custom menus and triggers
- Automatic creation of sheets and headers on setup (setupLabCasesSystem)
- Status-based row movement across sheets (onEdit and batchProcess)
- Duplicate detection and removal (removeDuplicateRows)
- Configurable sheet names, column headers, and status values via `config/labcasesConfig.gs`
- Environment-based multi-location deployment (Baytown, Humble, or all) using script properties
- Comprehensive error logging to an “Error Log” sheet and console
- Utilities for sheet management, column lookup, and error handling

## 3. Proposed Enhancements
### 3.1 Dynamic Header Lookup
- Replace hard-coded column indices with runtime header-to-column mapping
- Resilient to column reordering or header renaming

### 3.2 Doctor & Fee Tracking
- Add two new fields to each lab case: **Doctor** and **Total Fee**
- Doctor must be selected from a dropdown list with allowed values: **DKAM**, **DOBI**, **DCHI**
- Store and validate doctor (string) and fee (numeric)
- Expose these new headers and allowed doctor values in the configuration module

### 3.3 Monthly Doctor Fee Report
- Aggregate completed lab-case fees per doctor on a monthly basis
- Output a summary sheet with columns: [Month, Doctor, Total Fee]
- Support both manual invocation via custom menu and scheduled triggers (e.g. first of month)

## 4. Functional Requirements
1. **Header Map Utility**: Load header row at runtime; provide `getColumnIndex(headerName)` for all services
2. **Config Updates**: Extend `getLabCasesConfig()` with new headers `DOCTOR` and `TOTAL_FEE`, and add a new property `ALLOWED_DOCTORS` defaulting to [`DKAM`, `DOBI`, `DCHI`]
3. **Setup Service**: Modify sheet creation to include new columns, set correct header labels, and apply dropdown data validation on the Doctor column using `ALLOWED_DOCTORS`
4. **Data Processing**: Update onEdit and batchProcess to read/write Doctor and Total Fee values
5. **Aggregation Service**: Implement `generateDoctorFeeReport(month, year)` to:
   - Filter relevant rows by date and status
   - Sum fees grouped by doctor
   - Write or append results to a dedicated summary sheet
6. **Menu & Triggers**: Add menu item “Generate Monthly Fees by Doctor” and optional time-based trigger

## 5. Implementation Roadmap
1. **Audit & Refactor**
   - Identify all hard-coded column indices in utils and services
   - Introduce a header-mapping utility and replace static references
2. **Configuration Extension**
   - Add `DOCTOR` and `TOTAL_FEE` to `DEFAULT_COLUMN_HEADERS` and `DEFAULT_COLUMN_INDICES`
   - Update `getLabCasesConfig()` accordingly
3. **Setup Script Update**
   - Insert new headers into the LAB CASES sheet during setup
4. **Data Layer & Validation**
   - Extend data models to include doctor and fee
   - Add validation for fee numeric format
5. **Report Generator**
   - Build aggregation logic, grouping by doctor, summing fees
   - Create or select a summary sheet and write report rows
6. **UI & Automation**
   - Expose report generator in custom menu
   - (Optional) Configure time-driven trigger via Apps Script manifest or code
7. **Testing & Documentation**
   - Manual testing in sample spreadsheet
   - Update README.md and inline docs
8. **Deployment**
   - Deploy via `deployToBaytown`, `deployToHumble`, or `deployToAllLocations`

---
*Document created in `operations/labcases/PRD.md`*