/**
 * @fileoverview Setup service for the Lab Cases Management System.
 * Provides functions for initializing and configuring the system.
 */

/**
 * Sets up the Lab Cases Management System in a spreadsheet.
 * Creates necessary sheets, headers, and triggers.
 *
 * @param {string} [spreadsheetId=null] - Optional spreadsheet ID. If not provided, uses active spreadsheet.
 * @param {Object} [customConfig={}] - Optional custom configuration.
 * @returns {Object} Setup result with success status and message.
 */
function setupLabCasesSystem(spreadsheetId = null, customConfig = {}) {
  try {
    // Get the spreadsheet
    const ss = spreadsheetId ?
      SpreadsheetApp.openById(spreadsheetId) :
      SpreadsheetApp.getActiveSpreadsheet();

    if (!ss) {
      throw new Error(`Could not open spreadsheet: ${spreadsheetId || 'Active Spreadsheet'}`);
    }

    // Get configuration
    const config = getLabCasesConfig(customConfig);

    // Create all necessary sheets
    const sheets = getOrCreateSheets(ss, config);

    // Check for missing columns and add them if needed
    try {
      const sourceSheet = sheets.SOURCE;
      const headerRow = config.HEADER_ROW || 1;
      const headerValues = sourceSheet.getRange(headerRow, 1, 1, sourceSheet.getLastColumn()).getValues()[0];

      // Check if Doctor and Total Fee columns exist
      const doctorIndex = headerValues.findIndex(header => header === config.COLUMN_HEADERS.DOCTOR);
      const totalFeeIndex = headerValues.findIndex(header => header === config.COLUMN_HEADERS.TOTAL_FEE);

      // Add missing columns if needed
      let columnsAdded = false;
      if (doctorIndex === -1 || totalFeeIndex === -1) {
        const lastColumn = sourceSheet.getLastColumn();
        let newHeaders = [...headerValues];

        if (doctorIndex === -1) {
          console.log("Adding Doctor column");
          newHeaders.push(config.COLUMN_HEADERS.DOCTOR);
          columnsAdded = true;
        }

        if (totalFeeIndex === -1) {
          console.log("Adding Total Fee column");
          newHeaders.push(config.COLUMN_HEADERS.TOTAL_FEE);
          columnsAdded = true;
        }

        if (columnsAdded) {
          // Update the header row with new columns
          sourceSheet.getRange(headerRow, 1, 1, newHeaders.length).setValues([newHeaders]);
          sourceSheet.getRange(headerRow, 1, 1, newHeaders.length).setFontWeight("bold");
        }
      }

      // Get updated column indices after potentially adding new columns
      const columnIndices = getColumnIndices(sourceSheet, config.COLUMN_HEADERS, config.HEADER_ROW);

      // Apply dropdown validation for Doctor column
      const doctorCol = columnIndices.DOCTOR;
      if (doctorCol) {
        const rule = SpreadsheetApp.newDataValidation()
          .requireValueInList(config.ALLOWED_DOCTORS, true)
          .setAllowInvalid(false)
          .build();
        const numRows = sourceSheet.getMaxRows() - config.HEADER_ROW;
        if (numRows > 0) {
          sourceSheet.getRange(config.HEADER_ROW + 1, doctorCol, numRows, 1)
            .setDataValidation(rule);
        }
      }
    } catch (setupError) {
      logError("setupColumnsAndValidation", setupError, { sheetName: ss.getName() });
    }

    // Set up Error Log sheet with special headers
    const errorLogSheet = ss.getSheetByName("Error Log");
    if (!errorLogSheet) {
      const newErrorLogSheet = ss.insertSheet("Error Log");
      newErrorLogSheet.appendRow(ERROR_LOG_HEADERS);
      newErrorLogSheet.getRange(1, 1, 1, ERROR_LOG_HEADERS.length).setFontWeight("bold");
    } else if (errorLogSheet.getLastRow() === 0) {
      errorLogSheet.appendRow(ERROR_LOG_HEADERS);
      errorLogSheet.getRange(1, 1, 1, ERROR_LOG_HEADERS.length).setFontWeight("bold");
    }

    // Set up triggers
    setupTriggers(ss);

    // Create custom menu
    createCustomMenu(ss);

    return {
      success: true,
      message: "Lab Cases Management System setup completed successfully",
      spreadsheetId: ss.getId(),
      spreadsheetUrl: ss.getUrl()
    };
  } catch (error) {
    logError("setupLabCasesSystem", error, { spreadsheetId });
    return {
      success: false,
      message: `Setup failed: ${error.message || String(error)}`
    };
  }
}

/**
 * Sets up triggers for the Lab Cases Management System.
 *
 * @param {Object} ss - The spreadsheet object.
 * @returns {boolean} Whether the triggers were set up successfully.
 */
function setupTriggers(ss) {
  try {
    // Get existing triggers
    const triggers = ScriptApp.getUserTriggers(ss);
    let hasEditTrigger = false;
    let hasOpenTrigger = false;

    // Check if triggers already exist
    for (const trigger of triggers) {
      if (trigger.getEventType() === ScriptApp.EventType.ON_EDIT) {
        hasEditTrigger = true;
      } else if (trigger.getEventType() === ScriptApp.EventType.ON_OPEN) {
        hasOpenTrigger = true;
      }
    }

    // Create onEdit trigger if it doesn't exist
    if (!hasEditTrigger) {
      ScriptApp.newTrigger("onEdit")
        .forSpreadsheet(ss)
        .onEdit()
        .create();
      console.log("Created onEdit trigger");
    }

    // Create onOpen trigger if it doesn't exist
    if (!hasOpenTrigger) {
      ScriptApp.newTrigger("onOpen")
        .forSpreadsheet(ss)
        .onOpen()
        .create();
      console.log("Created onOpen trigger");
    }

    // Ensure monthly doctor fee report trigger exists
    try {
      const projectTriggers = ScriptApp.getProjectTriggers();
      const hasMonthly = projectTriggers.some(tr => tr.getHandlerFunction() === 'runMonthlyDoctorReport');
      if (!hasMonthly) {
        ScriptApp.newTrigger('runMonthlyDoctorReport')
          .timeBased()
          .onMonthDay(1)
          .atHour(1)
          .create();
        console.log('Created monthly doctor fee report trigger');
      }
    } catch (tError) {
      logError('setupMonthlyTrigger', tError, { spreadsheetId: ss.getId() });
    }
    return true;
  } catch (error) {
    logError("setupTriggers", error, { spreadsheetId: ss.getId() });
    return false;
  }
}

/**
 * Creates a custom menu in the spreadsheet.
 *
 * @param {Object} ss - The spreadsheet object.
 * @returns {boolean} Whether the menu was created successfully.
 */
function createCustomMenu(ss) {
  try {
    // Check if UI is available
    const ui = SpreadsheetApp.getUi();

    ui.createMenu('KamDental Lab Cases')
      .addItem('Run Batch Process', 'runBatchProcess')
      .addItem('Remove Duplicate Rows', 'removeDuplicateRows')
      .addItem('Generate Monthly Fees by Doctor', 'generateDoctorFeeReportMenu')
      .addSeparator()
      .addItem('Setup System', 'setupLabCasesSystem')
      .addItem('Show Documentation', 'showDocumentation')
      .addSeparator()
      .addSubMenu(ui.createMenu('Environment')
        .addItem('Configure Location', 'configureLocation')
        .addItem('List Environment Variables', 'listEnvironmentVars')
        .addItem('Delete Environment Variable', 'deleteEnvironmentVar'))
      .addSubMenu(ui.createMenu('Deployment')
        .addItem('Deploy to All Locations', 'deployToAllLocations')
        .addItem('Deploy to Baytown', 'deployToBaytown')
        .addItem('Deploy to Humble', 'deployToHumble'))
      .addToUi();

    console.log("Created custom menu");
    return true;
  } catch (error) {
    // If UI is not available (e.g., running from trigger)
    console.error(`Error creating custom menu: ${error.message || error}`);
    return false;
  }
}

/**
 * Shows documentation for the Lab Cases Management System.
 *
 * @returns {void}
 */
function showDocumentation() {
  try {
    const ui = SpreadsheetApp.getUi();
    const message = `
      Lab Cases Management System Documentation

      This system helps manage lab cases by automatically organizing cases
      based on their status. The system includes the following features:

      1. Status-based case organization
      2. Automatic timestamp updates
      3. Duplicate detection and removal
      4. Error logging and tracking

      Sheets:
      - LAB CASES: Main working sheet for active lab cases
      - Completed Cases: Cases that have been completed
      - Attention Required: Cases that need attention
      - Return: Cases to be returned
      - Cancelled Cases: Cases that have been cancelled
      - Re-do Cases: Cases that need to be redone
      - Scheduled + Paid: Cases that are scheduled and paid

      Workflow:
      1. Add new lab cases to the LAB CASES sheet
      2. Update the APPOINTMENT STATUS and INSURANCE STATUS columns
      3. The system will automatically move cases to the appropriate sheet
      4. Cases with ATTENTION NEEDED status will be copied to the Attention Required sheet
      5. Cases with SCHEDULED status and PAID insurance status will be copied to the Scheduled + Paid sheet

      Menu Options:
      - Run Batch Process: Processes all rows in the LAB CASES sheet
      - Remove Duplicate Rows: Removes duplicate entries from all sheets
      - Setup System: Sets up the system in a new spreadsheet
      - Show Documentation: Displays this documentation

      For help, contact Kam Dental support.
    `;

    ui.alert('Lab Cases Management System Documentation', message, ui.ButtonSet.OK);
  } catch (error) {
    logError("showDocumentation", error);
  }
}

/**
 * Deploys the Lab Cases Management System to a specific location.
 *
 * @param {string} location - The location to deploy to (e.g., 'baytown', 'humble').
 * @param {string} [spreadsheetId=null] - Optional spreadsheet ID. If not provided, prompts for ID.
 * @returns {Object} Deployment result with success status and message.
 */
function deployToLocation(location, spreadsheetId = null) {
  try {
    // If no spreadsheet ID provided, prompt for it
    if (!spreadsheetId) {
      const ui = SpreadsheetApp.getUi();
      const result = ui.prompt(
        `Deploy to ${location}`,
        'Please enter the spreadsheet ID:',
        ui.ButtonSet.OK_CANCEL
      );

      if (result.getSelectedButton() !== ui.Button.OK) {
        return {
          success: false,
          message: 'Deployment cancelled'
        };
      }

      spreadsheetId = result.getResponseText().trim();

      if (!spreadsheetId) {
        return {
          success: false,
          message: 'No spreadsheet ID provided'
        };
      }
    }

    // Set up the system in the target spreadsheet
    const setupResult = setupLabCasesSystem(spreadsheetId);

    if (!setupResult.success) {
      return setupResult;
    }

    return {
      success: true,
      message: `Successfully deployed to ${location}`,
      spreadsheetId,
      spreadsheetUrl: setupResult.spreadsheetUrl
    };
  } catch (error) {
    logError("deployToLocation", error, { location, spreadsheetId });
    return {
      success: false,
      message: `Deployment failed: ${error.message || String(error)}`
    };
  }
}

/**
 * Shows a UI for configuring environment variables for a location.
 *
 * @returns {Object} Result of the configuration.
 */
function configureLocation() {
  try {
    const ui = SpreadsheetApp.getUi();
    const result = ui.prompt(
      'Configure Location',
      'Enter the location name (e.g., baytown, humble):',
      ui.ButtonSet.OK_CANCEL
    );

    if (result.getSelectedButton() !== ui.Button.OK) {
      return {
        success: false,
        message: 'Configuration cancelled'
      };
    }

    const location = result.getResponseText().trim().toUpperCase();

    if (!location) {
      ui.alert('Error', 'Location name is required', ui.ButtonSet.OK);
      return {
        success: false,
        message: 'No location name provided'
      };
    }

    const spreadsheetResult = ui.prompt(
      `Configure ${location}`,
      'Enter the spreadsheet ID:',
      ui.ButtonSet.OK_CANCEL
    );

    if (spreadsheetResult.getSelectedButton() !== ui.Button.OK) {
      return {
        success: false,
        message: 'Configuration cancelled'
      };
    }

    const spreadsheetId = spreadsheetResult.getResponseText().trim();

    if (!spreadsheetId) {
      ui.alert('Error', 'Spreadsheet ID is required', ui.ButtonSet.OK);
      return {
        success: false,
        message: 'No spreadsheet ID provided'
      };
    }

    // Save to script properties
    const key = `SPREADSHEET_ID_${location}`;
    setEnv(key, spreadsheetId);

    ui.alert(
      'Success',
      `Environment variable ${key} has been set to ${spreadsheetId}`,
      ui.ButtonSet.OK
    );

    return {
      success: true,
      message: `Environment variable ${key} has been set to ${spreadsheetId}`,
      key,
      value: spreadsheetId
    };
  } catch (error) {
    logError("configureLocation", error);

    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        ui.ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }

    return {
      success: false,
      message: `Configuration failed: ${error.message || String(error)}`
    };
  }
}

/**
 * Lists all environment variables.
 *
 * @returns {Object} Result of the listing.
 */
function listEnvironmentVars() {
  try {
    const vars = listEnvVars();
    const ui = SpreadsheetApp.getUi();

    if (Object.keys(vars).length === 0) {
      ui.alert(
        'Environment Variables',
        'No environment variables found.',
        ui.ButtonSet.OK
      );

      return {
        success: true,
        message: 'No environment variables found',
        variables: {}
      };
    }

    let message = 'Environment Variables:\n\n';

    for (const key in vars) {
      message += `${key} = ${vars[key]}\n`;
    }

    ui.alert(
      'Environment Variables',
      message,
      ui.ButtonSet.OK
    );

    return {
      success: true,
      message: 'Environment variables listed',
      variables: vars
    };
  } catch (error) {
    logError("listEnvironmentVars", error);

    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }

    return {
      success: false,
      message: `Listing failed: ${error.message || String(error)}`
    };
  }
}

/**
 * Deletes an environment variable.
 *
 * @returns {Object} Result of the deletion.
 */
function deleteEnvironmentVar() {
  try {
    const ui = SpreadsheetApp.getUi();
    const result = ui.prompt(
      'Delete Environment Variable',
      'Enter the name of the environment variable to delete:',
      ui.ButtonSet.OK_CANCEL
    );

    if (result.getSelectedButton() !== ui.Button.OK) {
      return {
        success: false,
        message: 'Deletion cancelled'
      };
    }

    const key = result.getResponseText().trim();

    if (!key) {
      ui.alert('Error', 'Environment variable name is required', ui.ButtonSet.OK);
      return {
        success: false,
        message: 'No environment variable name provided'
      };
    }

    // Delete the environment variable
    deleteEnv(key);

    ui.alert(
      'Success',
      `Environment variable ${key} has been deleted`,
      ui.ButtonSet.OK
    );

    return {
      success: true,
      message: `Environment variable ${key} has been deleted`,
      key
    };
  } catch (error) {
    logError("deleteEnvironmentVar", error);

    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }

    return {
      success: false,
      message: `Deletion failed: ${error.message || String(error)}`
    };
  }
}

/**
 * Deploys the Lab Cases Management System to all configured locations.
 *
 * @returns {Object} Result of the deployment.
 */
function deployToAllLocations() {
  try {
    const locations = getSpreadsheetIds();
    const ui = SpreadsheetApp.getUi();

    if (Object.keys(locations).length === 0) {
      ui.alert(
        'Deployment Error',
        'No locations configured. Please configure locations first.',
        ui.ButtonSet.OK
      );

      return {
        success: false,
        message: 'No locations configured'
      };
    }

    const results = {};
    let successCount = 0;
    let failureCount = 0;

    for (const location in locations) {
      const spreadsheetId = locations[location];
      console.log(`Deploying to ${location} (${spreadsheetId})...`);

      try {
        const result = deployToLocation(location, spreadsheetId);
        results[location] = result;

        if (result.success) {
          successCount++;
        } else {
          failureCount++;
        }
      } catch (error) {
        logError("deployToAllLocations", error, { location, spreadsheetId });
        results[location] = {
          success: false,
          message: `Deployment failed: ${error.message || String(error)}`
        };
        failureCount++;
      }
    }

    let message = `Deployment completed with ${successCount} successes and ${failureCount} failures.\n\n`;

    for (const location in results) {
      message += `${location}: ${results[location].success ? 'Success' : 'Failed'} - ${results[location].message}\n`;
    }

    ui.alert(
      'Deployment Results',
      message,
      ui.ButtonSet.OK
    );

    return {
      success: successCount > 0 && failureCount === 0,
      message: `Deployment completed with ${successCount} successes and ${failureCount} failures`,
      results
    };
  } catch (error) {
    logError("deployToAllLocations", error);

    try {
      SpreadsheetApp.getUi().alert(
        'Error',
        `An error occurred: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      console.error('Failed to show error message:', error);
    }

    return {
      success: false,
      message: `Deployment failed: ${error.message || String(error)}`
    };
  }
}

// Wrap global functions with error handling
const setupSystem = withErrorHandling(setupLabCasesSystem, "setupLabCasesSystem", true);
const createMenu = withErrorHandling(createCustomMenu, "createCustomMenu", false);
const showDocs = withErrorHandling(showDocumentation, "showDocumentation", false);
const deploySystem = withErrorHandling(deployToLocation, "deployToLocation", true);
const configureEnv = withErrorHandling(configureLocation, "configureLocation", true);
const listEnv = withErrorHandling(listEnvironmentVars, "listEnvironmentVars", true);
const deleteEnvVar = withErrorHandling(deleteEnvironmentVar, "deleteEnvironmentVar", true);
const deployAll = withErrorHandling(deployToAllLocations, "deployToAllLocations", true);
