/**
 * Sheet Manager for the Treatment System
 *
 * Provides sheet-related utilities: getSheet, appendRow, deleteRow, etc.
 *
 * TODO: Implement sheet operations and helpers.
 */

// Example:
// function getSheetByName(spreadsheet, name) {
//   // Return the sheet object or create it if missing
// }

// function appendRow(sheet, rowData) {
//   // Add data to the bottom of the sheet
// }

// function deleteRow(sheet, rowIndex) {
//   // Remove a row from the sheet
// }