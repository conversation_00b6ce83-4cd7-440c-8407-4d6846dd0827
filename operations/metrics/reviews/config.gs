// --- CONFIGURATION ---
const CONFIG = {
  ACCOUNT_ID: 'YOUR_ACCOUNT_ID', // e.g., '123456789012345678901'
  LOCATION_ID: '7624171868593282794', // e.g., '1112223334445556667' (Numeric GMB Location ID, not Place ID)
  SPREADSHEET_ID: SpreadsheetApp.getActiveSpreadsheet().getId(),
  MONTHLY_COUNTS_SHEET_NAME: 'Monthly Review Counts', // Name of the sheet
  MAX_PAGES: 10, // Safety break for pagination
  OAUTH: {
    CLIENT_ID: ScriptProperties.getProperty('CLIENT_ID') || 'YOUR_GCP_PROJECT_OAUTH_CLIENT_ID',
    CLIENT_SECRET: ScriptProperties.getProperty('CLIENT_SECRET') || 'YOUR_GCP_PROJECT_OAUTH_CLIENT_SECRET',
    SCOPES: ['https://www.googleapis.com/auth/business.manage']
  }
}; 