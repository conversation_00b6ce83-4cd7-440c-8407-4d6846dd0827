// --- OAUTH2 AUTHENTICATION ---

/**
 * Creates and returns the OAuth2 service for Google My Business API.
 */
function getMyBusinessService() {
  return OAuth2.createService('GoogleMyBusiness')
      .setAuthorizationBaseUrl('https://accounts.google.com/o/oauth2/auth')
      .setTokenUrl('https://accounts.google.com/o/oauth2/token')
      .setClientId(CONFIG.OAUTH.CLIENT_ID)
      .setClientSecret(CONFIG.OAUTH.CLIENT_SECRET)
      .setCallbackFunction('authCallback')
      .setPropertyStore(PropertiesService.getUserProperties())
      .setScope(CONFIG.OAUTH.SCOPES[0])
      .setParam('access_type', 'offline')
      .setParam('prompt', 'consent');
}

/**
 * Handles the OAuth2 callback.
 */
function authCallback(request) {
  const service = getMyBusinessService();
  const authorized = service.handleCallback(request);
  if (authorized) {
    return HtmlService.createHtmlOutput('Success! You can close this tab.');
  } else {
    return HtmlService.createHtmlOutput('Denied. You can close this tab and try again.');
  }
}

/**
 * Resets the OAuth2 service authorization.
 */
function resetAuth() {
  const service = getMyBusinessService();
  service.reset();
  Logger.log("Authorization has been reset. Run the main function again to re-authorize.");
  SpreadsheetApp.getUi().alert("Auth Reset", "Authorization has been reset. Run 'Track Monthly Review Counts' to re-authorize.", SpreadsheetApp.getUi().ButtonSet.OK);
} 