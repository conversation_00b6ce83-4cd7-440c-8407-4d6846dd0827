// --- MAIN BUSINESS LOGIC ---

/**
 * Main function to calculate and write monthly review counts
 */
function calculateAndWriteMonthlyReviewCounts() {
  try {
    const reviews = getAllReviewsForLocation(CONFIG.ACCOUNT_ID, CONFIG.LOCATION_ID);
    if (reviews && reviews.length > 0) {
      const monthlyCounts = aggregateReviewsByMonth(reviews);
      writeMonthlyCountsToSheet(monthlyCounts);
      Logger.log(`Successfully calculated and wrote monthly review counts.`);
      SpreadsheetApp.getUi().alert(`Success`, `Successfully calculated and wrote monthly review counts.`, SpreadsheetApp.getUi().ButtonSet.OK);
    } else if (reviews) {
      Logger.log('No reviews found for this location or unable to fetch.');
      SpreadsheetApp.getUi().alert('Info', 'No reviews found to aggregate.', SpreadsheetApp.getUi().ButtonSet.OK);
      const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
      let sheet = ss.getSheetByName(CONFIG.MONTHLY_COUNTS_SHEET_NAME);
      if (sheet) {
        sheet.clearContents();
        const headers = ['Month (YYYY-MM)', 'Number of Reviews'];
        sheet.appendRow(headers);
        sheet.getRange("A1:B1").setFontWeight("bold");
      }
    } else {
      Logger.log('Failed to fetch reviews. Check logs for details.');
      SpreadsheetApp.getUi().alert('Error', 'Failed to fetch reviews. Check execution logs for details.', SpreadsheetApp.getUi().ButtonSet.OK);
    }
  } catch (e) {
    Logger.log(`Error in calculateAndWriteMonthlyReviewCounts: ${e.toString()}\n${e.stack}`);
    SpreadsheetApp.getUi().alert('Error', `An error occurred: ${e.message}`, SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

/**
 * Aggregates reviews by month of creation.
 * @param {Array<Object>} reviews - Array of review objects from the API.
 * @return {Object} An object where keys are "YYYY-MM" and values are review counts.
 */
function aggregateReviewsByMonth(reviews) {
  const monthlyCounts = {};
  const scriptTimeZone = Session.getScriptTimeZone();

  reviews.forEach(review => {
    if (review.createTime) {
      const createDate = new Date(review.createTime);
      const monthKey = Utilities.formatDate(createDate, scriptTimeZone, "yyyy-MM");

      if (monthlyCounts[monthKey]) {
        monthlyCounts[monthKey]++;
      } else {
        monthlyCounts[monthKey] = 1;
      }
    }
  });
  return monthlyCounts;
}

/**
 * Writes the aggregated monthly review counts to the specified sheet.
 */
function writeMonthlyCountsToSheet(monthlyCounts) {
  const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
  let sheet = ss.getSheetByName(CONFIG.MONTHLY_COUNTS_SHEET_NAME);
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.MONTHLY_COUNTS_SHEET_NAME);
  }

  sheet.clearContents();

  const headers = ['Month (YYYY-MM)', 'Number of Reviews'];
  sheet.appendRow(headers);
  sheet.getRange("A1:B1").setFontWeight("bold");

  const sortedMonths = Object.keys(monthlyCounts).sort();

  sortedMonths.forEach(monthKey => {
    sheet.appendRow([monthKey, monthlyCounts[monthKey]]);
  });

  for (let i = 1; i <= headers.length; i++) {
    sheet.autoResizeColumn(i);
  }
} 