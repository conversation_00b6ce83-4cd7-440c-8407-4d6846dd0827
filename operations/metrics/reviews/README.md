# Google My Business Reviews Tracker

This Google Apps Script project helps track and analyze Google My Business reviews by aggregating monthly review counts.

## Project Structure

The project follows a modular structure for better organization and maintainability:

- `config.gs`: Configuration constants and settings
- `core.gs`: Core business logic for review aggregation
- `api.gs`: Google My Business API interaction functions
- `auth.gs`: OAuth2 authentication handling
- `ui.gs`: UI/Menu related code

## Setup Instructions

1. Set up your Google Cloud Project and enable the Google My Business API
2. Create OAuth2 credentials (Client ID and Client Secret)
3. Add the OAuth2 library to your Apps Script project:
   - Script ID: `1B7FSrk5Zi6L1rSxxTDgDEusPzStr_y8jUnLflpxTMHoZxkcFmKDTuHoE`
4. Update the configuration in `config.gs`:
   - Set your `ACCOUNT_ID`
   - Set your `LOCATION_ID`
5. Run the `onOpen` function to create the menu
6. Use "List My Account/Location IDs" to find your account and location IDs

## Features

- Monthly review count aggregation
- Automatic pagination of review data
- Custom menu for easy access
- OAuth2 authentication management
- Detailed logging

## Dependencies

- OAuth2 library for Google Apps Script
- Google My Business API
- Google Sheets API