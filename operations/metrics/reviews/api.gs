// --- API CALLS ---

/**
 * Fetches all reviews for a given location, handling pagination.
 */
function getAllReviewsForLocation(accountId, locationId) {
  const service = getMyBusinessService();
  if (!service.hasAccess()) {
    Logger.log(
      "Authorization required. Please run the script again and authorize."
    );
    SpreadsheetApp.getUi().alert(
      "Authorization Required",
      "Please run the script again and authorize it to access your Google My Business data.",
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    return null;
  }

  let allReviews = [];
  let nextPageToken = null;
  let pageCount = 0;

  try {
    do {
      const requestUrl =
        `https://mybusiness.googleapis.com/v1/accounts/${accountId}/locations/${locationId}/reviews` +
        (nextPageToken ? `?pageToken=${nextPageToken}` : "");

      const response = UrlFetchApp.fetch(requestUrl, {
        headers: {
          Authorization: "Bearer " + service.getAccessToken(),
        },
        muteHttpExceptions: true,
      });

      const responseCode = response.getResponseCode();
      const responseBody = response.getContentText();

      if (responseCode === 200) {
        const jsonResponse = JSON.parse(responseBody);
        if (jsonResponse.reviews && jsonResponse.reviews.length > 0) {
          allReviews = allReviews.concat(jsonResponse.reviews);
        }
        nextPageToken = jsonResponse.nextPageToken;
      } else {
        Logger.log(`API Error: ${responseCode} - ${responseBody}`);
        throw new Error(
          `API Error fetching reviews: ${responseCode} - ${responseBody}`
        );
      }
      pageCount++;
    } while (nextPageToken && pageCount < CONFIG.MAX_PAGES);

    if (pageCount >= CONFIG.MAX_PAGES && nextPageToken) {
      Logger.log("Reached maximum page limit for fetching reviews.");
    }
    return allReviews;
  } catch (e) {
    Logger.log(
      `Error in getAllReviewsForLocation: ${e.toString()}\n${e.stack}`
    );
    return null;
  }
}

/**
 * Lists all accounts and locations for the authenticated user.
 */
function listMyAccountsAndLocations() {
  const service = getMyBusinessService();
  if (!service.hasAccess()) {
    Logger.log(
      "Authorization required. Please run this function again after authorizing."
    );
    SpreadsheetApp.getUi().alert(
      "Authorization Required",
      "Run this function again to authorize.",
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    return;
  }
  try {
    const accountsUrl = "https://mybusiness.googleapis.com/v1/accounts";
    const accountsResponse = UrlFetchApp.fetch(accountsUrl, {
      headers: { Authorization: "Bearer " + service.getAccessToken() },
      muteHttpExceptions: true,
    });
    const accountsData = JSON.parse(accountsResponse.getContentText());
    Logger.log("Accounts: %s", JSON.stringify(accountsData, null, 2));
    if (accountsData.accounts && accountsData.accounts.length > 0) {
      for (const account of accountsData.accounts) {
        Logger.log(
          `Account Name: ${account.accountName}, Account ID: ${
            account.name.split("/")[1]
          }`
        );
        const locationsUrl = `https://mybusiness.googleapis.com/v1/${account.name}/locations`;
        const locationsResponse = UrlFetchApp.fetch(locationsUrl, {
          headers: { Authorization: "Bearer " + service.getAccessToken() },
          muteHttpExceptions: true,
        });
        const locationsData = JSON.parse(locationsResponse.getContentText());
        Logger.log(
          "  Locations for account %s: %s",
          account.name,
          JSON.stringify(locationsData, null, 2)
        );
        if (locationsData.locations) {
          for (const location of locationsData.locations) {
            Logger.log(
              `    Location Name: ${location.locationName}, GMB Location ID: ${
                location.name.split("/")[3]
              }` + (location.placeId ? `, Place ID: ${location.placeId}` : "")
            );
          }
        } else {
          Logger.log(`    No locations found for account ${account.name}`);
        }
      }
      SpreadsheetApp.getUi().alert(
        "Check Logs",
        "Account and Location IDs have been logged. Check View > Logs.",
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } else {
      Logger.log("No accounts found.");
      SpreadsheetApp.getUi().alert(
        "No Accounts",
        "No GMB accounts found for this user.",
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    }
  } catch (e) {
    Logger.log(`Error listing accounts/locations: ${e.toString()}`);
    SpreadsheetApp.getUi().alert(
      "Error",
      `Error listing accounts/locations: ${e.message}`,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}
