function applyFormulasSthruW() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  const baytownSheet = ss.getSheetByName("Baytown EOD Form");
  const humbleSheet  = ss.getSheetByName("Humble EOD Form");

  const startRow = 3;

  try {
    if (baytownSheet) {
      applyFormulasToSheet(baytownSheet, startRow);
      applyPercentageFormatting(baytownSheet, startRow); // ✅ Ensure Baytown gets % format
    }
    if (humbleSheet) {
      applyFormulasToSheet(humbleSheet, startRow);
      
      if (baytownSheet) {
        copyFormatting(baytownSheet, humbleSheet, startRow);
      }
    }

    SpreadsheetApp.getUi().alert("Formulas and formatting applied successfully!");
  } catch (error) {
    Logger.log("Error: " + error.message);
    SpreadsheetApp.getUi().alert("Error: " + error.message);
  }
}

/**
 * Applies formulas (S, T, U, V, W) to the given sheet.
 */
function applyFormulasToSheet(sheet, startRow) {
  const lastRow = sheet.getLastRow();
  if (lastRow < startRow) return; 

  const numRows = lastRow - startRow + 1;

  applyFormulaToColumn(sheet, startRow, numRows, 19, `=IF(E3=0, 0, (E3-F3)/E3)`);
  applyFormulaToColumn(sheet, startRow, numRows, 20, `=IF(J3=0, 0, L3/J3)`);
  applyFormulaToColumn(sheet, startRow, numRows, 21, `=IF(Q3=0, 0, R3/Q3)`);
  applyFormulaToColumn(sheet, startRow, numRows, 22, `=IF(J3=0, 0, K3/J3)`);
  applyFormulaToColumn(sheet, startRow, numRows, 23, `=IF(N3=0, 0, (O3+P3)/N3)`);
}

/**
 * Ensures Baytown gets % formatting for S to W.
 */
function applyPercentageFormatting(sheet, startRow) {
  const lastRow = sheet.getLastRow();
  if (lastRow < startRow) return; 

  const numRows = lastRow - startRow + 1;

  for (let col = 19; col <= 23; col++) {
    try {
      sheet.getRange(startRow, col, numRows, 1).setNumberFormat("0%");
    } catch (error) {
      Logger.log(`Baytown Formatting error in Column ${col}: ${error.message}`);
    }
  }
}

/**
 * Copies number formatting from Baytown to Humble for columns S to W.
 */
function copyFormatting(sourceSheet, targetSheet, startRow) {
  const lastRow = targetSheet.getLastRow();
  if (lastRow < startRow) return;

  const numRows = lastRow - startRow + 1;

  for (let col = 19; col <= 23; col++) {
    try {
      const format = sourceSheet.getRange(startRow, col, numRows, 1).getNumberFormat();
      targetSheet.getRange(startRow, col, numRows, 1).setNumberFormat(format);
    } catch (error) {
      Logger.log(`Formatting error in Column ${col}: ${error.message}`);
    }
  }
}

/**
 * Helper function: sets formulas in a column.
 */
function applyFormulaToColumn(sheet, startRow, numRows, column, formulaTemplate) {
  const formulas = [];

  for (let i = 0; i < numRows; i++) {
    const row = startRow + i;

    const formulaForRow = formulaTemplate.replace(/3/g, row);
    formulas.push([formulaForRow]);
  }

  sheet.getRange(startRow, column, numRows, 1).setFormulas(formulas);
}
