function transferAndProcessData() {
  // IDs of the source and destination spreadsheets
  var sourceSpreadsheetId = '13hpkKHLn1rGPzVRh79PG7Qj-3IV1y_bds88zLzA8dV0'; // Source spreadsheet ID
  var destinationSpreadsheetId = '1Q7yC84vj3IQ2HuH6jHvEGC1T40qK8UQCRwbTlkes81g'; // Destination spreadsheet ID

  // Open the source spreadsheet and get the range of values
  var sourceSheet = SpreadsheetApp.openById(sourceSpreadsheetId).getSheetByName('Dashboard');
  var sourceRange = sourceSheet.getRange('X4:X55'); // Update range to X4:X55
  var sourceValues = sourceRange.getValues();

  // Log the source values to ensure they are fetched correctly
  Logger.log('Source Values: ' + JSON.stringify(sourceValues));

  // Process the values (adding every two)
  var processedValues = [];
  for (var i = 0; i < sourceValues.length; i += 2) {
    // Make sure there is a pair to add
    if (i + 1 < sourceValues.length) {
      var sum = (sourceValues[i][0] || 0) + (sourceValues[i + 1][0] || 0); // Add a fallback to 0 if a cell is empty
      processedValues.push([sum]);
    }
  }

  // Log the processed values to ensure they are processed correctly
  Logger.log('Processed Values: ' + JSON.stringify(processedValues));

  // Open the destination spreadsheet and set the processed values
  var destinationSheet = SpreadsheetApp.openById(destinationSpreadsheetId).getSheetByName('Bi-weekly payroll');
  var destinationRange = destinationSheet.getRange('I2:I' + (1 + processedValues.length)); // Adjusted range calculation
  destinationRange.setValues(processedValues);

  // Log a message indicating the script has completed
  Logger.log('Data transfer and processing completed.');
}
