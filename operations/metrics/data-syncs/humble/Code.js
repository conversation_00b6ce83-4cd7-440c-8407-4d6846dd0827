// Main function to be triggered on schedule
function syncHumbleData() {
  const sourceSheetName = "Humble MTD Reporting";
  const destinationSpreadsheetId = "1Zr0sjwqrChlcWnk6Ac9eQ70mMFNI4ddD0MYbrAfcjwQ";
  const destinationSheetName = "Humble data";
  
  processSheet(
    sourceSheetName,
    destinationSpreadsheetId,
    destinationSheetName,
    "Humble",
    [
      { label: "Net Production (Week-to-Date)", range: "B5" },
      { label: "Total Collections (Week-to-Date)", range: "E5" },
      { label: "Net Production (Month-to-Date)", range: "B11" },
      { label: "Total Collections (Month-to-Date)", range: "E11" },
      { label: "Average Daily Production", range: "C21" },
      { label: "Projected End of Month Production", range: "D21" },
      { label: "Status", range: "E21" },
      { label: "Case Acceptance Rate (MTD)", range: "G9" },
      { label: "Case Acceptance Rate (WTD)", range: "G3" },
      { label: "Inbound Call Answer Rate", range: "C59" },
      { label: "Dr. Kam<PERSON>di - Net Production", range: "C40" },
      { label: "Dr. <PERSON>mdi Irondi - Monthly Goal", range: "E40" },
      { label: "Dr. Kamdi Irondi - % to Goal", range: "F40" },
      { label: "Dr. Obinna Ezeji - Net Production", range: "C42" },
      { label: "Dr. Obinna Ezeji - Monthly Goal", range: "E42" },
      { label: "Dr. Obinna Ezeji - % to Goal", range: "F42" },
      { label: "Dr. Chinyere Enih - Net Production", range: "C45" },
      { label: "Kia Redfearn - Net Production", range: "C46" },
    ],
    { 
      tableStart: "A65",
      tableEnd: "J70",
      topProceduresColumn: 20, // Column T for Humble
      timestampColumn: 21      // Column U for Humble
    }
  );
}

// Function to set up time-based trigger for Humble
function createHumbleTrigger() {
  // Delete existing triggers
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => ScriptApp.deleteTrigger(trigger));
  
  // Create trigger for Thursday at 5 PM
  ScriptApp.newTrigger('syncHumbleData')
    .timeBased()
    .onWeekDay(ScriptApp.WeekDay.THURSDAY)
    .atHour(17)
    .create();
    
  Logger.log('Humble trigger created successfully');
}

// Main function to process the sheet
function processSheet(sourceSheetName, destinationSpreadsheetId, destinationSheetName, locationLabel, dataToExtract, tableDetails) {
  const sourceSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(sourceSheetName);
  if (!sourceSheet) {
    Logger.log("Error: Source sheet not found: " + sourceSheetName);
    return;
  }

  try {
    // Extract the data as an array of [header, value]
    const extractedData = dataToExtract.map(item => {
      const value = sourceSheet.getRange(item.range).getValue();
      return [item.label, value];
    });

    // Get current timestamp in MM-DD-YYYY format
    const today = new Date();
    const timestamp = (today.getMonth() + 1).toString().padStart(2, '0') + '-' +
                     today.getDate().toString().padStart(2, '0') + '-' +
                     today.getFullYear();

    // Process the table for top procedures
    const tableRange = sourceSheet.getRange(tableDetails.tableStart + ":" + tableDetails.tableEnd);
    const tableData = tableRange.getValues();
    const headers = tableData[0].slice(1);
    const rows = tableData.slice(1);
    const totals = headers.map((header, index) => ({
      procedure: header,
      total: rows.reduce((sum, row) => sum + (row[index + 1] || 0), 0),
    }));
    
    // Format top procedures as a single string
    const topProcedures = totals
      .sort((a, b) => b.total - a.total)
      .slice(0, 3)
      .map(({ procedure, total }) => `${procedure}: ${total}`)
      .join(', ');

    const destinationSpreadsheet = SpreadsheetApp.openById(destinationSpreadsheetId);
    const destinationSheet = destinationSpreadsheet.getSheetByName(destinationSheetName);
    if (!destinationSheet) {
      Logger.log("Error: Destination sheet not found: " + destinationSheetName);
      return;
    }

    // Find the next empty row
    const nextRow = destinationSheet.getLastRow() + 1;
    
    // Prepare the new row of data
    const newRow = [locationLabel, ...extractedData.map(data => data[1])];
    
    // Write the new row of data
    destinationSheet.getRange(nextRow, 1, 1, newRow.length).setValues([newRow]);
    
    // Write top procedures in the appropriate column
    destinationSheet.getRange(nextRow, tableDetails.topProceduresColumn).setValue(topProcedures);
    
    // Write timestamp in the appropriate column
    destinationSheet.getRange(nextRow, tableDetails.timestampColumn).setValue(timestamp);

    Logger.log(`Data successfully appended to row ${nextRow} in ${destinationSheetName}`);
    
  } catch (error) {
    Logger.log("Error processing sheet: " + error.toString());
  }
}

// Utility function to verify access and sheet names
function verifyHumbleSetup() {
  Logger.log("Verifying Humble setup...");
  
  // Verify source sheet
  const sourceSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName("Humble MTD Reporting");
  if (sourceSheet) {
    Logger.log("✓ Source sheet 'Humble MTD Reporting' found");
  } else {
    Logger.log("✗ Source sheet 'Humble MTD Reporting' not found");
  }
  
  // Verify destination spreadsheet and sheet
  try {
    const destSpreadsheet = SpreadsheetApp.openById("1Zr0sjwqrChlcWnk6Ac9eQ70mMFNI4ddD0MYbrAfcjwQ");
    Logger.log("✓ Destination spreadsheet found: " + destSpreadsheet.getName());
    
    const destSheet = destSpreadsheet.getSheetByName("Humble data");
    if (destSheet) {
      Logger.log("✓ Destination sheet 'Humble data' found");
    } else {
      Logger.log("✗ Destination sheet 'Humble data' not found");
    }
  } catch (e) {
    Logger.log("✗ Error accessing destination spreadsheet: " + e.toString());
  }
}
