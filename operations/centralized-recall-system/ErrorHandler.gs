/**
 * Error Handling for the Recall System
 * 
 * This file contains functions for logging and handling errors.
 */

/**
 * Logs an error to the Error Log sheet
 * 
 * @param {string} functionName - Where the error happened
 * @param {Error|string} error - The error that occurred
 * @param {Object} additionalInfo - Any extra information about the error
 * @param {string} spreadsheetId - ID of the spreadsheet (optional)
 */
function logError(functionName, error, additionalInfo = {}, spreadsheetId = null) {
  try {
    // Get the spreadsheet
    const ss = spreadsheetId ? 
      SpreadsheetApp.openById(spreadsheetId) : 
      SpreadsheetApp.getActiveSpreadsheet();
    
    if (!ss) {
      console.error(`Couldn't open spreadsheet for error logging`);
      console.error(`Original error in ${functionName}: ${error.message || error}`);
      return;
    }
    
    // Get or create error log sheet
    let errorSheet = ss.getSheetByName("Error Log");
    
    if (!errorSheet) {
      errorSheet = ss.insertSheet("Error Log");
      errorSheet.appendRow([
        "Date & Time", 
        "Function", 
        "Error Message", 
        "Error Details", 
        "Additional Info", 
        "User"
      ]);
      errorSheet.getRange(1, 1, 1, 6).setFontWeight("bold");
    }
    
    // Format error information
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorDetails = error instanceof Error ? error.stack : "No details available";
    
    // Log the error
    const timestamp = new Date();
    const user = Session.getActiveUser().getEmail() || "Unknown";
    const infoString = JSON.stringify(additionalInfo);
    
    errorSheet.appendRow([
      timestamp, 
      functionName, 
      errorMessage, 
      errorDetails, 
      infoString,
      user
    ]);
    
    // Also log to console
    console.error(`ERROR in ${functionName}: ${errorMessage}`);
    if (errorDetails) console.error(errorDetails);
    
  } catch (loggingError) {
    // If error logging itself fails
    console.error("Failed to log error:", loggingError);
    console.error(`Original error in ${functionName}:`, error);
  }
}
