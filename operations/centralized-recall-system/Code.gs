/**
 * Main Code for the Kam Dental Recall System
 *
 * This is the main file that Google Apps Script runs.
 * It connects all the other parts of the system.
 *
 * Created by Unified Dental
 */

// Import all the functions we need
// Note: In Google Apps Script, these imports aren't actually needed
// because all files are combined, but we include them for clarity
// and to make the code easier to understand.

/**
 * This function runs when someone opens the spreadsheet
 * It creates the custom menu
 */
function onOpen() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();

    // Create the menu
    SpreadsheetApp.getUi()
      .createMenu('Kam Dental Recall System')
      .addItem('Run Setup', 'setupRecallSystem')
      .addItem('Fix Missed Moves', 'processAllRowsFromMenu')
      .addItem('Fix Missing Dates', 'fixMissingDatesFromMenu')
      .addItem('Filter by Call Priority', 'filterByCallPriorityFromMenu')
      .addSeparator()
      .addSubMenu(SpreadsheetApp.getUi().createMenu('Deploy to Location')
        .addItem('Deploy to Baytown', 'deployToBaytown')
        .addItem('Deploy to Humble', 'deployToHumble')
        .addItem('Deploy to Other Location', 'deployToOtherLocation'))
      .addSeparator()
      .addItem('View Help', 'showHelp')
      .addToUi();

    console.log("Created Recall System menu");
  } catch (error) {
    console.error(`Error in onOpen: ${error.message || error}`);
  }
}

/**
 * This function is called from the menu to process all rows
 * It shows a confirmation dialog and then processes all rows
 */
function processAllRowsFromMenu() {
  try {
    const ui = SpreadsheetApp.getUi();

    // Show confirmation dialog
    const response = ui.alert(
      'Kam Dental Recall System - Fix Missed Moves',
      'This will scan the Master sheet for rows that should have been moved but weren\'t.\n\n' +
      'It will ONLY move rows with these statuses:\n' +
      '- SCHEDULED or ALREADY SCHEDULED\n' +
      '- DNC or DO NOT CALL\n' +
      '- CALL BACK REQUEST (moves to Sheet31)\n' +
      '- NO ANSWER in the Final Outcome column (Attempt 3)\n' +
      '- Any other valid status in the Final Outcome column\n\n' +
      'Note: "NO ANSWER" in the Status column will NOT trigger a move until it appears in the Final Outcome column.\n\n' +
      'Do you want to continue?',
      ui.ButtonSet.YES_NO
    );

    // If user clicked "Yes"
    if (response === ui.Button.YES) {
      // Show processing message
      const result = processAllRows();

      // Show result
      ui.alert(
        'Kam Dental Recall System - Processing Complete',
        `${result.message}\n\n` +
        (result.success ?
          `Processed ${result.processed} rows\nMoved ${result.moved} patients` :
          `Error: ${result.error}`),
        ui.ButtonSet.OK
      );
    }
  } catch (error) {
    console.error(`Error in processAllRowsFromMenu: ${error.message || error}`);

    // Show error message
    try {
      SpreadsheetApp.getUi().alert(
        'Kam Dental Recall System - Error',
        `An error occurred while processing rows: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      // Last resort if UI is not available
      console.error('Failed to show error message:', error);
    }
  }
}

/**
 * This function runs when someone edits the spreadsheet
 *
 * @param {Object} e - Information about what was edited
 */
function onEdit(e) {
  try {
    // Call the handleEdit function from RecallHandler.js
    handleEdit(e);
  } catch (error) {
    console.error(`Error in onEdit: ${error.message || error}`);
  }
}

/**
 * This function sets up the recall system
 *
 * @param {string} spreadsheetId - ID of the spreadsheet (optional)
 * @returns {Object} Result of the setup
 */
function setupRecallSystem(spreadsheetId = null) {
  try {
    // Call the setupRecallSystem function from RecallSetup.js
    // We use a different variable name to avoid recursion
    const result = _setupRecallSystem(spreadsheetId);
    return result;
  } catch (error) {
    console.error(`Error in setupRecallSystem: ${error.message || error}`);
    return {
      success: false,
      message: `Setup failed: ${error.message || String(error)}`
    };
  }
}

/**
 * This function shows help information
 */
function showHelp() {
  try {
    // Call the showHelp function from RecallSetup.js
    // We use a different variable name to avoid recursion
    _showHelp();
  } catch (error) {
    console.error(`Error showing help: ${error.message || error}`);
    try {
      SpreadsheetApp.getUi().alert('Error showing help. Please try again later.');
    } catch (e) {
      console.error('Failed to show help:', error);
    }
  }
}

/**
 * This function sets up the recall system in a specific spreadsheet
 *
 * @param {string} spreadsheetId - ID of the spreadsheet
 * @param {Object} customSettings - Custom settings (optional)
 * @returns {Object} Result of the setup
 */
function setupBySpreadsheetId(spreadsheetId, customSettings = {}) {
  if (!spreadsheetId) {
    return {
      success: false,
      message: "Spreadsheet ID is required"
    };
  }

  try {
    // Call the setupRecallSystem function from RecallSetup.js
    const result = _setupRecallSystem(spreadsheetId, customSettings);
    return result;
  } catch (error) {
    logError("setupBySpreadsheetId", error, { spreadsheetId, customSettings });
    return {
      success: false,
      message: `Setup failed: ${error.message || String(error)}`
    };
  }
}

/**
 * This function is called from the menu to fix missing dates
 * It shows a confirmation dialog and then fixes all missing dates
 */
function fixMissingDatesFromMenu() {
  try {
    const ui = SpreadsheetApp.getUi();

    // Show confirmation dialog
    const response = ui.alert(
      'Kam Dental Recall System - Fix Missing Dates',
      'This will scan the Master sheet for rows with missing dates and populate them based on existing notes.\n\n' +
      'It will:\n' +
      '- Add Next Attempt dates if Attempt 1 notes exist\n' +
      '- Add Final Attempt dates if Attempt 2 notes exist\n\n' +
      'Do you want to continue?',
      ui.ButtonSet.YES_NO
    );

    // If user clicked "Yes"
    if (response === ui.Button.YES) {
      // Show processing message
      const result = fixAllMissingDates();

      // Show result
      ui.alert(
        'Kam Dental Recall System - Processing Complete',
        `${result.message}\n\n` +
        (result.success ?
          `Processed ${result.processed} rows\nFixed ${result.fixed} missing dates` :
          `Error: ${result.error}`),
        ui.ButtonSet.OK
      );
    }
  } catch (error) {
    console.error(`Error in fixMissingDatesFromMenu: ${error.message || error}`);

    // Show error message
    try {
      SpreadsheetApp.getUi().alert(
        'Kam Dental Recall System - Error',
        `An error occurred while fixing missing dates: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      // Last resort if UI is not available
      console.error('Failed to show error message:', error);
    }
  }
}

/**
 * This function is called from the menu to filter by call priority
 * It shows a confirmation dialog and then applies the filter
 */
function filterByCallPriorityFromMenu() {
  try {
    const ui = SpreadsheetApp.getUi();

    // Show confirmation dialog
    const response = ui.alert(
      'Kam Dental Recall System - Filter by Call Priority',
      'This will filter the Master sheet to show rows in order of call priority:\n\n' +
      '1. Rows with no attempts (empty Date column) will appear first\n' +
      '2. Rows will then be sorted by Next Attempt Date\n\n' +
      'Do you want to continue?',
      ui.ButtonSet.YES_NO
    );

    // If user clicked "Yes"
    if (response === ui.Button.YES) {
      // Apply the filter
      const result = filterByCallPriority();

      // Show result
      ui.alert(
        'Kam Dental Recall System - Filtering Complete',
        result.success ?
          'Master sheet has been filtered by call priority.' :
          `Error: ${result.error}`,
        ui.ButtonSet.OK
      );
    }
  } catch (error) {
    console.error(`Error in filterByCallPriorityFromMenu: ${error.message || error}`);

    // Show error message
    try {
      SpreadsheetApp.getUi().alert(
        'Kam Dental Recall System - Error',
        `An error occurred while filtering: ${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      // Last resort if UI is not available
      console.error('Failed to show error message:', error);
    }
  }
}