/**
 * Deployment Tool for the Kam Dental Recall System
 *
 * This file contains functions for deploying the recall system to spreadsheets.
 *
 * Created by Unified Dental
 */

/**
 * Deploys the recall system to a specific spreadsheet
 *
 * @param {string} spreadsheetId - ID of the spreadsheet to deploy to
 * @param {Object} customSettings - Custom settings (optional)
 * @returns {Object} Result of the deployment
 */
function deployToSpreadsheet(spreadsheetId, customSettings = {}) {
  if (!spreadsheetId) {
    return {
      success: false,
      message: "Spreadsheet ID is required"
    };
  }

  try {
    console.log(`Deploying to spreadsheet: ${spreadsheetId}`);

    // Verify the spreadsheet exists and is accessible
    const ss = SpreadsheetApp.openById(spreadsheetId);
    if (!ss) {
      throw new Error(`Could not open spreadsheet with ID: ${spreadsheetId}`);
    }

    // Call the setup function
    const setupResult = setupBySpreadsheetId(spreadsheetId, customSettings);

    if (!setupResult.success) {
      throw new Error(`Setup failed: ${setupResult.message}`);
    }

    return {
      success: true,
      message: "Deployment successful",
      spreadsheetId: spreadsheetId,
      spreadsheetUrl: ss.getUrl()
    };
  } catch (error) {
    console.error(`Deployment failed: ${error.message || error}`);
    return {
      success: false,
      message: `Deployment failed: ${error.message || String(error)}`,
      error: error
    };
  }
}

/**
 * Creates a new spreadsheet and deploys the recall system to it
 *
 * @param {string} name - Name for the new spreadsheet
 * @param {Object} customSettings - Custom settings (optional)
 * @returns {Object} Result of the deployment
 */
function createNewRecallSpreadsheet(name = "Patient Recall System", customSettings = {}) {
  try {
    console.log(`Creating new spreadsheet: ${name}`);

    // Create a new spreadsheet
    const ss = SpreadsheetApp.create(name);
    const spreadsheetId = ss.getId();

    // Deploy to the new spreadsheet
    const deployResult = deployToSpreadsheet(spreadsheetId, customSettings);

    if (!deployResult.success) {
      throw new Error(`Deployment to new spreadsheet failed: ${deployResult.message}`);
    }

    return {
      success: true,
      message: `Created new spreadsheet "${name}" and deployed successfully`,
      spreadsheetId: spreadsheetId,
      spreadsheetUrl: ss.getUrl()
    };
  } catch (error) {
    console.error(`Failed to create and deploy to new spreadsheet: ${error.message || error}`);
    return {
      success: false,
      message: `Failed to create and deploy to new spreadsheet: ${error.message || String(error)}`,
      error: error
    };
  }
}

/**
 * Deploy the recall system to Baytown location with a popup for the spreadsheet ID
 *
 * @returns {Object} Result of the deployment
 */
function deployToBaytown() {
  return deployWithDialog("Baytown");
}

/**
 * Deploy the recall system to Humble location with a popup for the spreadsheet ID
 *
 * @returns {Object} Result of the deployment
 */
function deployToHumble() {
  return deployWithDialog("Humble");
}

/**
 * Deploy the recall system to a new location with a popup dialog for the spreadsheet ID
 *
 * @param {string} locationName - Name of the location (e.g., "Baytown", "Humble")
 * @returns {Object} Result of the deployment
 */
function deployWithDialog(locationName) {
  try {
    const ui = SpreadsheetApp.getUi();

    // Show an input dialog to get the spreadsheet ID
    const promptResponse = ui.prompt(
      `Deploy to ${locationName}`,
      `Please enter the Spreadsheet ID for ${locationName}:\n\n` +
      'You can find this in the spreadsheet URL:\n' +
      'https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit',
      ui.ButtonSet.OK_CANCEL
    );

    // Check if the user clicked "Cancel"
    if (promptResponse.getSelectedButton() === ui.Button.CANCEL) {
      return {
        success: false,
        message: "Deployment cancelled by user"
      };
    }

    // Get the spreadsheet ID from the input
    const spreadsheetId = promptResponse.getResponseText().trim();

    // Validate the spreadsheet ID
    if (!spreadsheetId) {
      ui.alert(
        "Error",
        "Spreadsheet ID cannot be empty.",
        ui.ButtonSet.OK
      );
      return {
        success: false,
        message: "Spreadsheet ID was empty"
      };
    }

    // Show a confirmation dialog
    const confirmResponse = ui.alert(
      `Confirm Deployment to ${locationName}`,
      `Are you sure you want to deploy the Kam Dental Recall System to ${locationName}?\n\n` +
      `Spreadsheet ID: ${spreadsheetId}`,
      ui.ButtonSet.YES_NO
    );

    // Check if the user clicked "No"
    if (confirmResponse === ui.Button.NO) {
      return {
        success: false,
        message: "Deployment cancelled by user"
      };
    }

    // Deploy to the spreadsheet
    const result = deployToSpreadsheet(spreadsheetId);

    // Show the result
    if (result.success) {
      ui.alert(
        "Deployment Successful",
        `The Kam Dental Recall System has been successfully deployed to ${locationName}.\n\n` +
        `Spreadsheet URL: ${result.spreadsheetUrl}`,
        ui.ButtonSet.OK
      );
    } else {
      ui.alert(
        "Deployment Failed",
        `Failed to deploy to ${locationName}:\n\n${result.message}`,
        ui.ButtonSet.OK
      );
    }

    return result;
  } catch (error) {
    console.error(`Deployment to ${locationName} failed: ${error.message || error}`);

    // Show error message
    try {
      SpreadsheetApp.getUi().alert(
        "Deployment Error",
        `An error occurred while deploying to ${locationName}:\n\n${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      // Last resort if UI is not available
      console.error('Failed to show error message:', error);
    }

    return {
      success: false,
      message: `Deployment to ${locationName} failed: ${error.message || String(error)}`,
      error: error
    };
  }
}

/**
 * Generic function to deploy to a new location
 *
 * @param {string} locationName - Name of the location
 * @param {string} spreadsheetId - ID of the spreadsheet
 * @param {Object} customSettings - Custom settings (optional)
 * @returns {Object} Result of the deployment
 */
function deployToNewLocation(locationName, spreadsheetId, customSettings = {}) {
  if (!locationName || !spreadsheetId) {
    return {
      success: false,
      message: "Location name and spreadsheet ID are required"
    };
  }

  try {
    console.log(`Deploying to ${locationName} (${spreadsheetId})`);

    // Deploy to the spreadsheet
    const result = deployToSpreadsheet(spreadsheetId, customSettings);

    if (result.success) {
      result.message = `Successfully deployed to ${locationName}`;
    }

    return result;
  } catch (error) {
    console.error(`Deployment to ${locationName} failed: ${error.message || error}`);
    return {
      success: false,
      message: `Deployment to ${locationName} failed: ${error.message || String(error)}`,
      error: error
    };
  }
}

/**
 * Deploy the recall system to any location with a popup for the location name and spreadsheet ID
 * This function is called from the menu
 *
 * @returns {Object} Result of the deployment
 */
function deployToOtherLocation() {
  try {
    const ui = SpreadsheetApp.getUi();

    // First, get the location name
    const locationPrompt = ui.prompt(
      'Deploy to New Location',
      'Please enter the name of the location (e.g., "West Office", "Downtown"):',
      ui.ButtonSet.OK_CANCEL
    );

    // Check if the user clicked "Cancel"
    if (locationPrompt.getSelectedButton() === ui.Button.CANCEL) {
      return {
        success: false,
        message: "Deployment cancelled by user"
      };
    }

    // Get the location name
    const locationName = locationPrompt.getResponseText().trim();

    // Validate the location name
    if (!locationName) {
      ui.alert(
        "Error",
        "Location name cannot be empty.",
        ui.ButtonSet.OK
      );
      return {
        success: false,
        message: "Location name was empty"
      };
    }

    // Now deploy using the standard dialog
    return deployWithDialog(locationName);
  } catch (error) {
    console.error(`Deployment to new location failed: ${error.message || error}`);

    // Show error message
    try {
      SpreadsheetApp.getUi().alert(
        "Deployment Error",
        `An error occurred while deploying to new location:\n\n${error.message || String(error)}`,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    } catch (e) {
      // Last resort if UI is not available
      console.error('Failed to show error message:', error);
    }

    return {
      success: false,
      message: `Deployment to new location failed: ${error.message || String(error)}`,
      error: error
    };
  }
}