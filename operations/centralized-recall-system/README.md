# Recall System

A simple system for managing patient recalls in Google Sheets.

## What This Does

This system helps you manage patient recalls by:

1. Automatically moving patients between sheets based on their status
2. Setting follow-up dates automatically
3. Tracking when changes were made
4. Logging errors for troubleshooting

## How to Set Up

### Method 1: Manual Setup

1. Open your Google Sheets spreadsheet
2. Go to Extensions > Apps Script
3. Copy all the files from this folder into your Apps Script project
4. Save the project
5. Run the `setupRecallSystem()` function
6. Grant permissions when asked

### Method 2: Using Clasp (Command Line)

This method allows you to deploy the system directly from your computer:

1. Make sure you have Node.js installed
2. Install clasp globally: `npm install -g @google/clasp`
3. Login to Google: `clasp login`
4. Create a new Apps Script project: `clasp create --title "Recall System" --rootDir .`
   - Or use an existing script ID: `clasp clone <scriptId>`
5. Push the code: `clasp push`
6. Open the script: `clasp open`
7. Run the `setupRecallSystem()` function
8. Grant permissions when asked

### Quick Deploy with Our Script

We've included a deployment script to make this process easier:

1. Make sure you have a script ID (either from an existing project or by creating a new one)
2. Run: `./deploy.sh <scriptId>`
3. Follow the prompts to complete the deployment

## How to Use

The system will create these sheets:

- **Master**: Your main working sheet with all active recalls
- **Reactivated**: Patients who scheduled appointments
- **NO ANSWER**: Patients who didn't answer calls
- **DNC**: Patients who should not be called
- **Sheet31**: Patients who requested callbacks
- **WILL CALL US BACK**: Patients who will call back
- **DISCONNECTED**: Disconnected phone numbers
- **Error Log**: System errors (for troubleshooting)

### Automatic Features

1. When you add notes to Attempt 1 (Column E), the Next Attempt date (Column G) is automatically set to 2 weeks later

2. When you add notes to Attempt 2 (Column H), the Final Attempt date (Column J) is automatically set to 2 months later

3. When you change a patient's Status (Column C) or Final Outcome (Column K), they are automatically moved to the appropriate sheet

4. The Last Edited column (Column M) is updated whenever you make changes

## Setting Up in Another Spreadsheet

To set up this system in another spreadsheet:

1. Get the ID of the spreadsheet (the long string in the URL)
2. Run this function in the Apps Script editor:
   ```javascript
   setupBySpreadsheetId('paste-spreadsheet-id-here');
   ```

## Troubleshooting

If something isn't working:

1. Check the Error Log sheet for details about what went wrong
2. Make sure all the required sheets exist
3. Check that the column headers match what the system expects
4. Make sure you have edit permissions for the spreadsheet

## Need Help?

Contact your system administrator for assistance.
