/**
 * Setup for the Kam Dental Recall System
 *
 * This file contains functions for setting up the recall system.
 *
 * Created by Unified Dental
 */

/**
 * Sets up the recall system in a spreadsheet
 *
 * @param {string} spreadsheetId - ID of the spreadsheet (optional)
 * @param {Object} customSettings - Custom settings (optional)
 * @returns {Object} Result of the setup
 */
function _setupRecallSystem(spreadsheetId = null, customSettings = {}) {
  try {
    // Get the spreadsheet
    const ss = spreadsheetId ?
      SpreadsheetApp.openById(spreadsheetId) :
      SpreadsheetApp.getActiveSpreadsheet();

    if (!ss) {
      throw new Error(`Could not open spreadsheet`);
    }

    // Get settings
    const settings = getSettings(customSettings);

    // Create all necessary sheets
    const sheets = createAllSheets(ss, settings);

    // Set up triggers
    setupTriggers(ss);

    // We'll skip direct menu creation since it requires UI context
    // Instead, we'll ensure an onOpen trigger exists to create the menu when the user opens the spreadsheet
    try {
      // Check if onOpen trigger already exists
      const triggers = ScriptApp.getUserTriggers(ss);
      let hasOpenTrigger = false;

      for (const trigger of triggers) {
        if (trigger.getEventType() === ScriptApp.EventType.ON_OPEN) {
          hasOpenTrigger = true;
          break;
        }
      }

      // Create onOpen trigger if it doesn't exist
      if (!hasOpenTrigger) {
        ScriptApp.newTrigger("onOpen")
          .forSpreadsheet(ss)
          .onOpen()
          .create();
        console.log("Created onOpen trigger for menu creation");
      }
    } catch (error) {
      console.log("Error setting up onOpen trigger: " + error.message);
      // Non-fatal error, continue with setup
    }

    return {
      success: true,
      message: "Recall system setup completed successfully",
      spreadsheetId: ss.getId(),
      spreadsheetUrl: ss.getUrl()
    };
  } catch (error) {
    logError("setupRecallSystem", error, { spreadsheetId, customSettings });
    return {
      success: false,
      message: `Setup failed: ${error.message || String(error)}`,
      error: error.message || String(error)
    };
  }
}

/**
 * Sets up the necessary triggers for the system
 *
 * @param {Object} spreadsheet - The spreadsheet
 * @returns {boolean} Whether setup was successful
 */
function setupTriggers(spreadsheet) {
  try {
    // Check existing triggers
    const triggers = ScriptApp.getUserTriggers(spreadsheet);
    let hasEditTrigger = false;

    for (const trigger of triggers) {
      if (trigger.getEventType() === ScriptApp.EventType.ON_EDIT) {
        hasEditTrigger = true;
        break;
      }
    }

    // Create onEdit trigger if it doesn't exist
    if (!hasEditTrigger) {
      ScriptApp.newTrigger("onEdit")
        .forSpreadsheet(spreadsheet)
        .onEdit()
        .create();
      console.log("Created onEdit trigger");
    } else {
      console.log("onEdit trigger already exists");
    }

    return true;
  } catch (error) {
    logError("setupTriggers", error, { spreadsheetId: spreadsheet.getId() });
    return false;
  }
}

/**
 * Creates a custom menu in the spreadsheet
 * Note: This function can only be called from a UI context (like onOpen)
 *
 * @param {Object} spreadsheet - The spreadsheet
 * @returns {boolean} Whether menu creation was successful
 */
function createCustomMenu(spreadsheet) {
  try {
    // Check if we're in a context where UI is available
    try {
      // This will throw an error if UI is not available
      SpreadsheetApp.getUi();
    } catch (e) {
      // UI is not available, so we'll create an onOpen trigger instead
      console.log("UI not available. Creating onOpen trigger for menu creation.");

      // Check if onOpen trigger already exists
      const triggers = ScriptApp.getUserTriggers(spreadsheet);
      let hasOpenTrigger = false;

      for (const trigger of triggers) {
        if (trigger.getEventType() === ScriptApp.EventType.ON_OPEN) {
          hasOpenTrigger = true;
          break;
        }
      }

      // Create onOpen trigger if it doesn't exist
      if (!hasOpenTrigger) {
        ScriptApp.newTrigger("onOpen")
          .forSpreadsheet(spreadsheet)
          .onOpen()
          .create();
        console.log("Created onOpen trigger for menu creation");
      }

      return true;
    }

    // If we get here, UI is available
    SpreadsheetApp.getUi()
      .createMenu('Kam Dental Recall System')
      .addItem('Run Setup', 'setupRecallSystem')
      .addSeparator()
      .addItem('View Help', 'showHelp')
      .addToUi();

    return true;
  } catch (error) {
    logError("createCustomMenu", error, { spreadsheetId: spreadsheet.getId() });
    return false;
  }
}

/**
 * Shows help information for the recall system
 */
function _showHelp() {
  try {
    const ui = SpreadsheetApp.getUi();
    const message = `
      Kam Dental Recall System Help

      This system helps manage patient recalls by automatically organizing patients
      based on their recall status.

      How it works:

      1. When you add notes to Attempt 1, the Next Attempt date is automatically set to 2 weeks later

      2. When you add notes to Attempt 2, the Final Attempt date is automatically set to 2 months later

      3. When you change a patient's Status (Column C) or Final Outcome (Column K), they are
         automatically moved to the appropriate sheet

      Menu Options:
      - Run Setup: Sets up the recall system in a new spreadsheet
      - Fix Missed Moves: Checks for rows that should have been moved but weren't
      - Fix Missing Dates: Populates missing Next Attempt and Final Attempt dates based on existing notes
      - Filter by Call Priority: Sorts the Master sheet to show rows in order of call priority

      Sheets:
      - Master: Main working sheet for active recalls
      - Reactivated: Patients who have scheduled appointments
      - NO ANSWER: Patients who didn't answer calls
      - DNC: Do Not Call list
      - Sheet31: Patients requesting callbacks
      - WILL CALL US BACK: Patients who will call back
      - DISCONNECTED: Disconnected phone numbers

      For help, contact Kam Dental support.

      Created by Unified Dental
    `;

    ui.alert('Kam Dental Recall System Help', message, ui.ButtonSet.OK);
  } catch (error) {
    logError("showHelp", error);
    // Try to show a simpler alert if the rich alert fails
    try {
      SpreadsheetApp.getUi().alert('Kam Dental Recall System - Error showing help. Check the Error Log sheet.');
    } catch (e) {
      console.error('Failed to show help:', error);
    }
  }
}
