/**
 * Column Finder for the Kam Dental Recall System
 *
 * This file contains functions for finding and working with columns.
 *
 * Created by Unified Dental
 */

/**
 * Finds the position of each important column based on the header row
 *
 * @param {Array} headerRow - The first row of the sheet with column names
 * @param {Object} defaultPositions - Default positions to use if headers aren't found
 * @returns {Object} The position of each important column
 */
function findColumnPositions(headerRow, defaultPositions) {
  try {
    const positions = { ...defaultPositions }; // Start with defaults

    // Look through each header cell
    headerRow.forEach((header, index) => {
      const headerText = String(header).trim().toUpperCase();

      // Match headers based on keywords
      if (headerText.includes("PATIENT") && headerText.includes("NAME"))
        positions.PATIENT_NAME = index + 1;

      if (headerText === "AGE" || headerText.includes("AGE"))
        positions.AGE = index + 1;

      if (headerText === "STATUS" || headerText.includes("STATUS"))
        positions.STATUS = index + 1;

      if (headerText === "DATE" || (headerText.includes("DATE") && !headerText.includes("DUE") && !headerText.includes("ATTEMPT")))
        positions.DATE = index + 1;

      if (headerText.includes("ATTEMPT 1") || (headerText.includes("ATTEMPT") && headerText.includes("1") && headerText.includes("NOTE")))
        positions.ATTEMPT1_NOTES = index + 1;

      if (headerText.includes("CALLER #1") || headerText.includes("CALLER 1"))
        positions.CALLER1 = index + 1;

      if (headerText.includes("NEXT ATTEMPT") || (headerText.includes("NEXT") && headerText.includes("DUE")))
        positions.NEXT_ATTEMPT_DATE = index + 1;

      if (headerText.includes("ATTEMPT 2") || (headerText.includes("ATTEMPT") && headerText.includes("2") && headerText.includes("NOTE")))
        positions.ATTEMPT2_NOTES = index + 1;

      if (headerText.includes("CALLER #2") || headerText.includes("CALLER 2"))
        positions.CALLER2 = index + 1;

      if (headerText.includes("FINAL ATTEMPT") && headerText.includes("DUE"))
        positions.FINAL_ATTEMPT_DATE = index + 1;

      if (headerText.includes("ATTEMPT 3") || headerText.includes("FINAL OUTCOME"))
        positions.FINAL_OUTCOME = index + 1;

      if (headerText.includes("CALLER #3") || headerText.includes("CALLER 3"))
        positions.CALLER3 = index + 1;

      if (headerText.includes("LAST EDITED") || headerText.includes("EDITED"))
        positions.LAST_EDITED = index + 1;
    });

    return positions;
  } catch (error) {
    logError("findColumnPositions", error, { headerRow });
    return defaultPositions; // Fall back to defaults on error
  }
}

/**
 * Updates the "Last Edited" timestamp for a row
 *
 * @param {Object} sheet - The sheet to update
 * @param {number} row - The row number
 * @param {number} lastEditedColumn - The column number for "Last Edited"
 */
function updateLastEdited(sheet, row, lastEditedColumn) {
  try {
    const timestamp = new Date();
    sheet.getRange(row, lastEditedColumn).setValue(timestamp);
  } catch (error) {
    logError("updateLastEdited", error, {
      sheetName: sheet.getName(),
      row,
      lastEditedColumn
    });
  }
}

/**
 * Sets a future date in a cell
 *
 * @param {Object} sheet - The sheet to update
 * @param {number} row - The row number
 * @param {number} column - The column number
 * @param {number} daysToAdd - Days to add to reference date
 * @param {number} monthsToAdd - Months to add to reference date
 * @param {number} referenceColumn - Column number to use as reference date (optional)
 * @returns {Date} The date that was set
 */
function setFutureDate(sheet, row, column, daysToAdd = 0, monthsToAdd = 0, referenceColumn = null) {
  try {
    let baseDate;

    // If a reference column is provided, use that date as the base
    if (referenceColumn) {
      const referenceValue = sheet.getRange(row, referenceColumn).getValue();

      // If the reference cell has a valid date, use it
      if (referenceValue && referenceValue instanceof Date) {
        baseDate = new Date(referenceValue);
      } else {
        // If no valid date in reference cell, use today's date
        baseDate = new Date();
      }
    } else {
      // No reference column, use today's date
      baseDate = new Date();
    }

    // Add days and months
    if (daysToAdd > 0) {
      baseDate.setDate(baseDate.getDate() + daysToAdd);
    }

    if (monthsToAdd > 0) {
      baseDate.setMonth(baseDate.getMonth() + monthsToAdd);
    }

    sheet.getRange(row, column).setValue(baseDate);
    return baseDate;
  } catch (error) {
    logError("setFutureDate", error, {
      sheetName: sheet.getName(),
      row,
      column,
      daysToAdd,
      monthsToAdd,
      referenceColumn
    });
    return null;
  }
}

/**
 * Gets a patient's name from a row of data
 *
 * @param {Array} rowData - All the data in the row
 * @param {Object} columnPositions - The positions of each column
 * @returns {string} The patient's name
 */
function getPatientName(rowData, columnPositions) {
  try {
    // Convert to 0-based index for array access
    const nameIndex = columnPositions.PATIENT_NAME - 1;

    if (nameIndex >= 0 && nameIndex < rowData.length) {
      const name = rowData[nameIndex];
      return name ? String(name).trim() : "Unknown Patient";
    }

    return "Unknown Patient";
  } catch (error) {
    logError("getPatientName", error, { rowData, columnPositions });
    return "Unknown Patient";
  }
}
