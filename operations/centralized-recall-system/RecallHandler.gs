/**
 * Recall Handler for the Kam Dental Recall System
 *
 * This file contains the main functions for handling recall operations.
 *
 * Created by Unified Dental
 */

/**
 * Processes an edit in the spreadsheet
 *
 * @param {Object} event - The edit event from Google Sheets
 * @param {string} spreadsheetId - ID of the spreadsheet (optional)
 */
function handleEdit(event, spreadsheetId = null) {
  try {
    if (!event || !event.range) {
      console.log('No edit information found');
      return;
    }

    const range = event.range;
    const sheet = range.getSheet();
    const editedValue = range.getValue();

    // Get settings and spreadsheet
    const settings = getSettings();
    const ss = spreadsheetId ?
      SpreadsheetApp.openById(spreadsheetId) :
      SpreadsheetApp.getActiveSpreadsheet();

    // Only process edits in the Master sheet
    if (sheet.getName() !== settings.SHEET_NAMES.MASTER) {
      return;
    }

    // Get all sheets (creating any that don't exist)
    const sheets = createAllSheets(ss, settings);

    // Get header row and column positions
    const headerRow = getHeaderRow(sheet, settings.COLUMN_HEADERS);
    const columnPositions = findColumnPositions(headerRow, settings.COLUMN_POSITIONS);

    // Process the edit
    processEdit(sheet, range, editedValue, columnPositions, sheets, settings);

  } catch (error) {
    logError("handleEdit", error, {
      sheetName: event && event.range ? event.range.getSheet().getName() : null,
      range: event && event.range ? event.range.getA1Notation() : null,
      value: event && event.range ? event.range.getValue() : null
    }, spreadsheetId);
  }
}

/**
 * Processes an edit in the Master sheet
 *
 * @param {Object} sheet - The sheet being edited
 * @param {Object} range - The range being edited
 * @param {*} editedValue - The new value
 * @param {Object} columnPositions - The positions of each column
 * @param {Object} sheets - All sheets in the system
 * @param {Object} settings - System settings
 */
function processEdit(sheet, range, editedValue, columnPositions, sheets, settings) {
  try {
    const row = range.getRow();
    const col = range.getColumn();

    // Skip header row
    if (row === 1) return;

    // Get all data in the row
    const rowData = sheet.getRange(row, 1, 1, sheet.getLastColumn()).getValues()[0];
    const patientName = getPatientName(rowData, columnPositions);

    // Update Last Edited timestamp for any edit
    updateLastEdited(sheet, row, columnPositions.LAST_EDITED);

    // Handle different types of edits

    // 1. Status column edit (Column C)
    if (col === columnPositions.STATUS) {
      handleStatusChange(sheet, rowData, row, editedValue, patientName, sheets, settings);
    }

    // 2. Final Outcome column edit (Column K)
    else if (col === columnPositions.FINAL_OUTCOME) {
      handleFinalOutcome(sheet, rowData, row, editedValue, patientName, sheets, settings);
    }

    // 3. Attempt 1 Notes edit (Column E) - Set Next Attempt Date
    else if (col === columnPositions.ATTEMPT1_NOTES) {
      const value = String(editedValue).trim();
      if (value !== "") {
        setFutureDate(
          sheet,
          row,
          columnPositions.NEXT_ATTEMPT_DATE,
          settings.TIME_PERIODS.NEXT_ATTEMPT_DAYS,
          0,
          columnPositions.DATE // Use Date column (Column D) as reference
        );
      }
    }

    // 4. Attempt 2 Notes edit (Column H) - Set Final Attempt Date
    else if (col === columnPositions.ATTEMPT2_NOTES) {
      const value = String(editedValue).trim();
      if (value !== "") {
        setFutureDate(
          sheet,
          row,
          columnPositions.FINAL_ATTEMPT_DATE,
          0,
          settings.TIME_PERIODS.FINAL_ATTEMPT_MONTHS,
          columnPositions.NEXT_ATTEMPT_DATE // Use Next Attempt Date (Column G) as reference
        );
      }
    }

    // Check if we need to populate any missing dates based on existing notes
    checkAndPopulateMissingDates(sheet, row, columnPositions, settings);

  } catch (error) {
    logError("processEdit", error, {
      sheetName: sheet.getName(),
      range: range.getA1Notation(),
      value: editedValue
    });
  }
}

/**
 * Handles changes to the Status column (Column C)
 *
 * @param {Object} sheet - The sheet being edited
 * @param {Array} rowData - All data in the row
 * @param {number} row - The row number
 * @param {*} editedValue - The new status value
 * @param {string} patientName - The patient's name
 * @param {Object} sheets - All sheets in the system
 * @param {Object} settings - System settings
 */
function handleStatusChange(sheet, rowData, row, editedValue, patientName, sheets, settings) {
  try {
    // Convert to uppercase for case-insensitive comparison
    const upperValue = String(editedValue).toUpperCase();

    // Special handling for "NO ANSWER" status
    if (upperValue === "NO ANSWER") {
      // For "NO ANSWER", we don't move the row until it's in the Final Outcome column
      console.log(`Status "NO ANSWER" for ${patientName} - keeping in Master sheet until Final Outcome`);

      // Update the Last Edited timestamp
      updateLastEdited(sheet, row, settings);

      // Set the Next Attempt Date if this is the first attempt
      const columnPositions = getColumnPositions(sheet, settings);
      const attempt1Index = columnPositions.ATTEMPT_1 - 1; // Convert to 0-based for array
      const attempt2Index = columnPositions.ATTEMPT_2 - 1; // Convert to 0-based for array
      const nextAttemptIndex = columnPositions.NEXT_ATTEMPT_DATE - 1; // Convert to 0-based for array
      const finalAttemptIndex = columnPositions.FINAL_ATTEMPT_DATE - 1; // Convert to 0-based for array

      // If there are notes in Attempt 1 and no Next Attempt Date, set it to 14 days from now
      if (attempt1Index >= 0 && nextAttemptIndex >= 0 &&
          rowData[attempt1Index] && !rowData[nextAttemptIndex]) {
        const nextAttemptDate = new Date();
        nextAttemptDate.setDate(nextAttemptDate.getDate() + 14); // 14 days from now
        sheet.getRange(row, columnPositions.NEXT_ATTEMPT_DATE).setValue(nextAttemptDate);
        console.log(`Set Next Attempt Date for ${patientName} to ${nextAttemptDate.toDateString()}`);
      }

      // If there are notes in Attempt 2 and no Final Attempt Date, set it to 2 months from now
      if (attempt2Index >= 0 && finalAttemptIndex >= 0 &&
          rowData[attempt2Index] && !rowData[finalAttemptIndex]) {
        const finalAttemptDate = new Date();
        finalAttemptDate.setMonth(finalAttemptDate.getMonth() + 2); // 2 months from now
        sheet.getRange(row, columnPositions.FINAL_ATTEMPT_DATE).setValue(finalAttemptDate);
        console.log(`Set Final Attempt Date for ${patientName} to ${finalAttemptDate.toDateString()}`);
      }

      return;
    }

    // For all other statuses, check if they should trigger a move
    const targetSheetName = settings.STATUS_ACTIONS[upperValue];
    if (targetSheetName) {
      const targetSheet = sheets[Object.keys(settings.SHEET_NAMES).find(
        key => settings.SHEET_NAMES[key] === targetSheetName
      )];

      if (targetSheet) {
        movePatient(sheet, targetSheet, rowData, row, patientName, upperValue);
      } else {
        console.log(`Target sheet "${targetSheetName}" not found for status "${upperValue}"`);
      }
    } else {
      console.log(`Status "${editedValue}" does not trigger a move.`);
    }
  } catch (error) {
    logError("handleStatusChange", error, {
      sheetName: sheet.getName(),
      row,
      status: editedValue,
      patientName
    });
  }
}

/**
 * Handles changes to the Final Outcome column (Column K)
 *
 * @param {Object} sheet - The sheet being edited
 * @param {Array} rowData - All data in the row
 * @param {number} row - The row number
 * @param {*} editedValue - The new outcome value
 * @param {string} patientName - The patient's name
 * @param {Object} sheets - All sheets in the system
 * @param {Object} settings - System settings
 */
function handleFinalOutcome(sheet, rowData, row, editedValue, patientName, sheets, settings) {
  try {
    // Convert to uppercase for case-insensitive comparison
    const upperValue = String(editedValue).toUpperCase();

    // Special handling for "NO ANSWER" in the Final Outcome column
    if (upperValue === "NO ANSWER") {
      // Move to the NO ANSWER sheet
      const noAnswerSheet = sheets[Object.keys(settings.SHEET_NAMES).find(
        key => settings.SHEET_NAMES[key] === settings.SHEET_NAMES.NO_ANSWER
      )];

      if (noAnswerSheet) {
        movePatient(sheet, noAnswerSheet, rowData, row, patientName, upperValue);
        console.log(`Moved patient "${patientName}" to NO ANSWER sheet after final attempt`);
      } else {
        console.log(`NO ANSWER sheet not found for final outcome "${upperValue}"`);
      }
      return;
    }

    // For all other outcomes, check if they should trigger a move
    const targetSheetName = settings.STATUS_ACTIONS[upperValue];
    if (targetSheetName) {
      const targetSheet = sheets[Object.keys(settings.SHEET_NAMES).find(
        key => settings.SHEET_NAMES[key] === targetSheetName
      )];

      if (targetSheet) {
        movePatient(sheet, targetSheet, rowData, row, patientName, upperValue);
      } else {
        console.log(`Target sheet "${targetSheetName}" not found for outcome "${upperValue}"`);
      }
    } else {
      console.log(`Final outcome "${editedValue}" does not trigger a move.`);
    }
  } catch (error) {
    logError("handleFinalOutcome", error, {
      sheetName: sheet.getName(),
      row,
      outcome: editedValue,
      patientName
    });
  }
}

/**
 * Scans all rows in the Master sheet and only moves rows that should have been moved but weren't
 * This is a manual backup in case the onEdit trigger fails
 *
 * @param {string} spreadsheetId - ID of the spreadsheet (optional)
 * @returns {Object} Result with counts of processed rows
 */
function processAllRows(spreadsheetId = null) {
  try {
    // Get settings and spreadsheet
    const settings = getSettings();
    const ss = spreadsheetId ?
      SpreadsheetApp.openById(spreadsheetId) :
      SpreadsheetApp.getActiveSpreadsheet();

    // Get the Master sheet
    const masterSheet = ss.getSheetByName(settings.SHEET_NAMES.MASTER);
    if (!masterSheet) {
      throw new Error(`Master sheet "${settings.SHEET_NAMES.MASTER}" not found`);
    }

    // Get all sheets (creating any that don't exist)
    const sheets = createAllSheets(ss, settings);

    // Get header row and column positions
    const headerRow = getHeaderRow(masterSheet, settings.COLUMN_HEADERS);
    const columnPositions = findColumnPositions(headerRow, settings.COLUMN_POSITIONS);

    // Get all data from the Master sheet
    const lastRow = masterSheet.getLastRow();
    if (lastRow <= 1) {
      return {
        success: true,
        message: "No data found in Master sheet",
        processed: 0,
        moved: 0
      };
    }

    const data = masterSheet.getRange(2, 1, lastRow - 1, masterSheet.getLastColumn()).getValues();

    // Process each row
    let processed = 0;
    let moved = 0;

    // Define the specific statuses we're looking for that should have triggered a move
    const statusesToCheck = ["SCHEDULED", "ALREADY SCHEDULED", "DNC", "DO NOT CALL", "CALL BACK REQUEST"];

    // We need to process rows from bottom to top to avoid index issues when rows are deleted
    for (let i = data.length - 1; i >= 0; i--) {
      const rowData = data[i];
      const rowIndex = i + 2; // +2 because we start at row 2 (after header) and i is 0-based
      processed++;

      // Get patient name
      const patientName = getPatientName(rowData, columnPositions);
      let shouldMove = false;
      let targetSheet = null;
      let statusValue = "";

      // Check Status column (Column C) - but ONLY for specific statuses
      const statusColIndex = columnPositions.STATUS - 1; // Convert to 0-based for array
      if (statusColIndex >= 0 && statusColIndex < rowData.length) {
        const status = rowData[statusColIndex];
        if (status) {
          const upperStatus = String(status).toUpperCase();

          // Only check specific statuses that should have triggered a move
          if (statusesToCheck.includes(upperStatus)) {
            const targetSheetName = settings.STATUS_ACTIONS[upperStatus];

            if (targetSheetName) {
              targetSheet = sheets[Object.keys(settings.SHEET_NAMES).find(
                key => settings.SHEET_NAMES[key] === targetSheetName
              )];

              if (targetSheet) {
                shouldMove = true;
                statusValue = upperStatus;
              }
            }
          }
        }
      }

      // If not flagged for move by status, check Final Outcome column (Column K)
      if (!shouldMove) {
        const outcomeColIndex = columnPositions.FINAL_OUTCOME - 1; // Convert to 0-based for array
        if (outcomeColIndex >= 0 && outcomeColIndex < rowData.length) {
          const outcome = rowData[outcomeColIndex];
          if (outcome) {
            const upperOutcome = String(outcome).toUpperCase();

            // Special handling for "NO ANSWER" in the Final Outcome column
            if (upperOutcome === "NO ANSWER") {
              const noAnswerSheet = sheets[Object.keys(settings.SHEET_NAMES).find(
                key => settings.SHEET_NAMES[key] === settings.SHEET_NAMES.NO_ANSWER
              )];

              if (noAnswerSheet) {
                targetSheet = noAnswerSheet;
                shouldMove = true;
                statusValue = upperOutcome;
              }
            }
            // For all other outcomes, check if they should trigger a move
            else if (upperOutcome && settings.STATUS_ACTIONS[upperOutcome]) {
              const targetSheetName = settings.STATUS_ACTIONS[upperOutcome];

              targetSheet = sheets[Object.keys(settings.SHEET_NAMES).find(
                key => settings.SHEET_NAMES[key] === targetSheetName
              )];

              if (targetSheet) {
                shouldMove = true;
                statusValue = upperOutcome;
              }
            }
          }
        }
      }

      // Only move if we determined this row should have been moved
      if (shouldMove && targetSheet) {
        movePatient(masterSheet, targetSheet, rowData, rowIndex, patientName, statusValue);
        moved++;
      }
    }

    return {
      success: true,
      message: `Processed ${processed} rows, moved ${moved} patients`,
      processed,
      moved
    };
  } catch (error) {
    logError("processAllRows", error, { spreadsheetId });
    return {
      success: false,
      message: `Error processing rows: ${error.message || String(error)}`,
      error: error.message || String(error)
    };
  }
}

/**
 * Checks for missing dates and populates them based on existing notes
 *
 * @param {Object} sheet - The sheet to check
 * @param {number} row - The row number to check (1-based)
 * @param {Object} columnPositions - The positions of each column
 * @param {Object} settings - System settings
 * @returns {boolean} Whether any dates were populated
 */
function checkAndPopulateMissingDates(sheet, row, columnPositions, settings) {
  try {
    // Get the row data
    const rowData = sheet.getRange(row, 1, 1, sheet.getLastColumn()).getValues()[0];
    let datesPopulated = false;

    // Check if Attempt 1 Notes exist but Next Attempt Date is missing
    const attempt1NotesIndex = columnPositions.ATTEMPT1_NOTES - 1;
    const nextAttemptDateIndex = columnPositions.NEXT_ATTEMPT_DATE - 1;

    if (attempt1NotesIndex >= 0 && attempt1NotesIndex < rowData.length &&
        nextAttemptDateIndex >= 0 && nextAttemptDateIndex < rowData.length) {

      const attempt1Notes = rowData[attempt1NotesIndex];
      const nextAttemptDate = rowData[nextAttemptDateIndex];

      // If there are notes but no next attempt date
      if (attempt1Notes && String(attempt1Notes).trim() !== "" &&
          (!nextAttemptDate || nextAttemptDate === "")) {

        // Set the next attempt date
        setFutureDate(
          sheet,
          row,
          columnPositions.NEXT_ATTEMPT_DATE,
          settings.TIME_PERIODS.NEXT_ATTEMPT_DAYS,
          0,
          columnPositions.DATE // Use Date column (Column D) as reference
        );

        datesPopulated = true;
        console.log(`Populated Next Attempt Date for row ${row} based on existing Attempt 1 Notes`);
      }
    }

    // Check if Attempt 2 Notes exist but Final Attempt Date is missing
    const attempt2NotesIndex = columnPositions.ATTEMPT2_NOTES - 1;
    const finalAttemptDateIndex = columnPositions.FINAL_ATTEMPT_DATE - 1;

    if (attempt2NotesIndex >= 0 && attempt2NotesIndex < rowData.length &&
        finalAttemptDateIndex >= 0 && finalAttemptDateIndex < rowData.length) {

      const attempt2Notes = rowData[attempt2NotesIndex];
      const finalAttemptDate = rowData[finalAttemptDateIndex];

      // If there are notes but no final attempt date
      if (attempt2Notes && String(attempt2Notes).trim() !== "" &&
          (!finalAttemptDate || finalAttemptDate === "")) {

        // Set the final attempt date
        setFutureDate(
          sheet,
          row,
          columnPositions.FINAL_ATTEMPT_DATE,
          0,
          settings.TIME_PERIODS.FINAL_ATTEMPT_MONTHS,
          columnPositions.NEXT_ATTEMPT_DATE // Use Next Attempt Date (Column G) as reference
        );

        datesPopulated = true;
        console.log(`Populated Final Attempt Date for row ${row} based on existing Attempt 2 Notes`);
      }
    }

    return datesPopulated;
  } catch (error) {
    logError("checkAndPopulateMissingDates", error, {
      sheetName: sheet.getName(),
      row
    });
    return false;
  }
}

/**
 * Fixes all missing dates in the Master sheet
 *
 * @param {string} spreadsheetId - ID of the spreadsheet (optional)
 * @returns {Object} Result with counts of fixed dates
 */
function fixAllMissingDates(spreadsheetId = null) {
  try {
    // Get settings and spreadsheet
    const settings = getSettings();
    const ss = spreadsheetId ?
      SpreadsheetApp.openById(spreadsheetId) :
      SpreadsheetApp.getActiveSpreadsheet();

    // Get the Master sheet
    const masterSheet = ss.getSheetByName(settings.SHEET_NAMES.MASTER);
    if (!masterSheet) {
      throw new Error(`Master sheet "${settings.SHEET_NAMES.MASTER}" not found`);
    }

    // Get header row and column positions
    const headerRow = getHeaderRow(masterSheet, settings.COLUMN_HEADERS);
    const columnPositions = findColumnPositions(headerRow, settings.COLUMN_POSITIONS);

    // Get all data from the Master sheet
    const lastRow = masterSheet.getLastRow();
    if (lastRow <= 1) {
      return {
        success: true,
        message: "No data found in Master sheet",
        processed: 0,
        fixed: 0
      };
    }

    // Process each row
    let processed = 0;
    let fixed = 0;

    for (let row = 2; row <= lastRow; row++) {
      processed++;

      // Check and populate missing dates
      const dateFixed = checkAndPopulateMissingDates(masterSheet, row, columnPositions, settings);
      if (dateFixed) {
        fixed++;
      }
    }

    return {
      success: true,
      message: `Processed ${processed} rows, fixed ${fixed} missing dates`,
      processed,
      fixed
    };
  } catch (error) {
    logError("fixAllMissingDates", error, { spreadsheetId });
    return {
      success: false,
      message: `Error fixing missing dates: ${error.message || String(error)}`,
      error: error.message || String(error)
    };
  }
}

/**
 * Filters the Master sheet to show rows in order of call priority
 *
 * @param {string} spreadsheetId - ID of the spreadsheet (optional)
 * @returns {Object} Result of the filtering operation
 */
function filterByCallPriority(spreadsheetId = null) {
  try {
    // Get settings and spreadsheet
    const settings = getSettings();
    const ss = spreadsheetId ?
      SpreadsheetApp.openById(spreadsheetId) :
      SpreadsheetApp.getActiveSpreadsheet();

    // Get the Master sheet
    const masterSheet = ss.getSheetByName(settings.SHEET_NAMES.MASTER);
    if (!masterSheet) {
      throw new Error(`Master sheet "${settings.SHEET_NAMES.MASTER}" not found`);
    }

    // Get header row and column positions
    const headerRow = getHeaderRow(masterSheet, settings.COLUMN_HEADERS);
    const columnPositions = findColumnPositions(headerRow, settings.COLUMN_POSITIONS);

    // Clear any existing filters
    if (masterSheet.getFilter()) {
      masterSheet.getFilter().remove();
    }

    // Get the data range
    const lastRow = masterSheet.getLastRow();
    const lastCol = masterSheet.getLastColumn();
    if (lastRow <= 1) {
      return {
        success: true,
        message: "No data to filter in Master sheet"
      };
    }

    // Create a filter
    const range = masterSheet.getRange(1, 1, lastRow, lastCol);
    const filter = range.createFilter();

    // First, sort by Date column (Column D) - empty dates first
    const dateCol = columnPositions.DATE;
    if (dateCol) {
      filter.sort(dateCol, true); // true = ascending (empty cells first)
    }

    // Then, sort by Next Attempt Date (Column G) - ascending
    const nextAttemptCol = columnPositions.NEXT_ATTEMPT_DATE;
    if (nextAttemptCol) {
      filter.sort(nextAttemptCol, true); // true = ascending
    }

    // Scroll to the top
    masterSheet.setActiveRange(masterSheet.getRange(2, 1));

    return {
      success: true,
      message: "Filtered Master sheet by call priority"
    };
  } catch (error) {
    logError("filterByCallPriority", error, { spreadsheetId });
    return {
      success: false,
      message: `Error filtering by call priority: ${error.message || String(error)}`,
      error: error.message || String(error)
    };
  }
}