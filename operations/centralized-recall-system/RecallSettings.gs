/**
 * Settings for the Recall System
 *
 * This file contains all the settings and options for the recall system.
 * You can change these settings to customize how the system works.
 */

/**
 * Names of all the sheets used in the system
 */
const SHEET_NAMES = {
  MASTER: "Master",                  // Main sheet with all active recalls
  SCHEDULED: "Reactivated",          // Patients who scheduled appointments
  NO_ANSWER: "NO ANSWER",            // Patients who didn't answer
  DO_NOT_CALL: "DNC",                // Patients who should not be called
  CALLBACK: "Sheet31",               // Patients who requested callbacks
  WILL_CALL_BACK: "WILL CALL US BACK", // Patients who will call back
  DISCONNECTED: "DISCONNECTED",      // Disconnected phone numbers
  ERROR_LOG: "Error Log"             // System errors and issues
};

/**
 * Column headers used in the recall sheets
 */
const COLUMN_HEADERS = [
  "PATIENT NAME",
  "AGE",
  "STATUS",
  "Date",
  "ATTEMPT 1 - Please type out any notes here.",
  "CALLER #1",
  "Next Attempt Due on:",
  "ATTEMPT 2 - Please type out any notes here.",
  "CALLER #2",
  "Final Attempt Due on:",
  "ATTEMPT 3 - Please select the final outcome",
  "CALLER #3",
  "Last Edited"
];

/**
 * Default column positions (1-based, where A=1, B=2, etc.)
 */
const COLUMN_POSITIONS = {
  PATIENT_NAME: 1,    // Column A
  AGE: 2,             // Column B
  STATUS: 3,          // Column C
  DATE: 4,            // Column D
  ATTEMPT1_NOTES: 5,  // Column E
  CALLER1: 6,         // Column F
  NEXT_ATTEMPT_DATE: 7, // Column G
  ATTEMPT2_NOTES: 8,  // Column H
  CALLER2: 9,         // Column I
  FINAL_ATTEMPT_DATE: 10, // Column J
  FINAL_OUTCOME: 11,  // Column K
  CALLER3: 12,        // Column L
  LAST_EDITED: 13     // Column M
};

/**
 * Status values that trigger moving a patient to another sheet
 * Note: "NO ANSWER" is not included here as it should only move after the final attempt
 */
const STATUS_ACTIONS = {
  "SCHEDULED": SHEET_NAMES.SCHEDULED,
  "ALREADY SCHEDULED": SHEET_NAMES.SCHEDULED,
  "DNC": SHEET_NAMES.DO_NOT_CALL,
  "DO NOT CALL": SHEET_NAMES.DO_NOT_CALL,
  "CALL BACK REQUEST": SHEET_NAMES.CALLBACK,
  "WILL CALL US BACK": SHEET_NAMES.WILL_CALL_BACK,
  "DISCONNECTED": SHEET_NAMES.DISCONNECTED
};

/**
 * Time periods for scheduling follow-up attempts
 */
const TIME_PERIODS = {
  NEXT_ATTEMPT_DAYS: 14,      // 2 weeks
  FINAL_ATTEMPT_MONTHS: 2     // 2 months
};

/**
 * Headers for the error log sheet
 */
const ERROR_LOG_HEADERS = [
  "Date & Time",
  "Function",
  "Error Message",
  "Error Details",
  "Additional Info",
  "User"
];

/**
 * Get all settings, with optional custom overrides
 *
 * @param {Object} customSettings - Optional custom settings to override defaults
 * @returns {Object} The complete settings object
 */
function getSettings(customSettings = {}) {
  return {
    SHEET_NAMES: { ...SHEET_NAMES, ...(customSettings.SHEET_NAMES || {}) },
    COLUMN_HEADERS: customSettings.COLUMN_HEADERS || COLUMN_HEADERS,
    COLUMN_POSITIONS: { ...COLUMN_POSITIONS, ...(customSettings.COLUMN_POSITIONS || {}) },
    STATUS_ACTIONS: { ...STATUS_ACTIONS, ...(customSettings.STATUS_ACTIONS || {}) },
    TIME_PERIODS: { ...TIME_PERIODS, ...(customSettings.TIME_PERIODS || {}) },
    ERROR_LOG_HEADERS: customSettings.ERROR_LOG_HEADERS || ERROR_LOG_HEADERS
  };
}
