#!/bin/bash

# Deployment script for the Recall System

# Check if script ID is provided
if [ "$1" == "" ]; then
  echo "Usage: ./deploy.sh <scriptId>"
  echo "  scriptId: The Google Apps Script project ID to deploy to"
  echo ""
  echo "If you don't have a script ID yet, you can create a new project with:"
  echo "  pnpm run create"
  exit 1
fi

# Update the .clasp.json file with the provided script ID
SCRIPT_ID=$1
echo "{\"scriptId\":\"$SCRIPT_ID\",\"rootDir\":\".\"}" > .clasp.json
echo "Updated .clasp.json with script ID: $SCRIPT_ID"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  pnpm install
fi

# Login to clasp if needed
echo "Checking clasp login status..."
pnpm exec clasp login

# Push the code to the Apps Script project
echo "Pushing code to Google Apps Script project..."
pnpm exec clasp push -f

echo "Deployment complete! Your code has been pushed to the Google Apps Script project."
echo "You can open the script editor with: pnpm exec clasp open"
