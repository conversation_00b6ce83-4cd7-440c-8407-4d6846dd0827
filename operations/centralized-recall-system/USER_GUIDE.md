# Kam Dental Recall System - User Guide

## What is this?

The Kam Dental Recall System helps you keep track of patients who need to be called back. It's like a smart to-do list that organizes patients based on when they need to be called and what happened during previous calls.

## Getting Started

1. Open your spreadsheet
2. Look for the "Kam Dental Recall System" menu at the top
3. If you don't see the menu, click "Run Setup" first

## How to Use the System


### Making Calls

When you call a patient:

1. Find the patient in the "Master" sheet
2. Write notes about your call in the "Attempt 1" column
3. The system will automatically set a date for the next call (2 weeks later)
4. When it's time for the second call, write notes in the "Attempt 2" column
5. The system will automatically set a date for the final call (2 months after the second call)

### Changing Patient Status

If a patient schedules an appointment or asks not to be called:

1. Change their "Status" (Column C) to one of these:
   - SCHEDULED: Patient made an appointment
   - ALREADY SCHEDULED: Patient already has an appointment
   - DNC or DO NOT CALL: Patient doesn't want to be called
   - CALL BACK REQUEST: <PERSON>ient requested a callback (moves to Sheet31)
   - WILL CALL US BACK: <PERSON><PERSON> said they will call back
   - DISCONNECTED: Phone number doesn't work

The system will automatically move the patient to the right sheet based on their status:

- For CALL BACK REQUEST: The patient will be moved to Sheet31, which triggers an automation that adds a follow-up task to our Click-Up system
- For INSURANCE status: The patient needs attention from the insurance department - please escalate the issue to them

## Special Features

### Fix Missing Dates

If you notice some dates are missing:

1. Click "Kam Dental Recall System" in the menu
2. Select "Fix Missing Dates"
3. Click "Yes" to confirm
4. The system will add missing dates based on your notes

### Filter by Call Priority

To see which patients to call first:

1. Click "Kam Dental Recall System" in the menu
2. Select "Filter by Call Priority"
3. Click "Yes" to confirm
4. The system will sort patients so you know who to call first:
   - Patients with no attempts will be at the top
   - Then patients sorted by their next call date

### Fix Missed Moves

If some patients didn't move to the right sheet:

1. Click "Kam Dental Recall System" in the menu
2. Select "Fix Missed Moves"
3. Click "Yes" to confirm
4. The system will move patients with these statuses to the correct sheets:
   - SCHEDULED or ALREADY SCHEDULED
   - DNC or DO NOT CALL
   - CALL BACK REQUEST (moves to Sheet31)
   - Any valid status in the Final Outcome column

## Understanding the Sheets

- **Master**: Your main working sheet with all active recalls
- **Reactivated**: Patients who scheduled appointments
- **NO ANSWER**: Patients who didn't answer calls
- **DNC**: Patients who don't want to be called
- **Sheet31**: Patients who requested callbacks (triggers automation)
- **WILL CALL US BACK**: Patients who said they'll call back
- **DISCONNECTED**: Phone numbers that don't work

## Deploying to Multiple Locations (Baytown, Humble, etc.)

To set up this system in a new spreadsheet for a different location:

### Easy Method (Using the Menu)

1. In the original spreadsheet with the recall system:
   - Click on "Kam Dental Recall System" in the menu
   - Select "Deploy to Location"
   - Choose the location you want to deploy to:
     - "Deploy to Baytown"
     - "Deploy to Humble"
     - "Deploy to Other Location" (for any other office)

2. When prompted:
   - Enter the Spreadsheet ID of the target spreadsheet
   - (For "Other Location" option, you'll also be asked to enter the location name)

3. Confirm the deployment when asked

4. That's it! The system will be set up in the target spreadsheet automatically.

### Finding a Spreadsheet ID

To find a Spreadsheet ID:
- Open the spreadsheet you want to set up
- Look at the URL in your browser
- The long string of letters and numbers between "/d/" and "/edit" is the Spreadsheet ID
- Example: In `https://docs.google.com/spreadsheets/d/abc123xyz456789/edit`, the ID is `abc123xyz456789`

### After Deployment

- Open the new spreadsheet
- You should see the "Kam Dental Recall System" menu
- If not, refresh the page or run the setup function manually

## Need Help?

1. Click "Kam Dental Recall System" in the menu
2. Select "View Help"
3. Or contact Kam Dental support

---

Created by Unified Dental for Kam Dental
