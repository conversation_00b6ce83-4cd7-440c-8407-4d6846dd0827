/**
 * Sheet Management for the Recall System
 * 
 * This file contains functions for managing sheets and moving data between them.
 */

/**
 * Creates all the sheets needed for the recall system
 * 
 * @param {Object} spreadsheet - The spreadsheet to set up
 * @param {Object} settings - System settings
 * @returns {Object} All the created sheets
 */
function createAllSheets(spreadsheet, settings) {
  try {
    const sheets = {};
    const sheetNames = settings.SHEET_NAMES;
    
    // Create each required sheet
    for (const [key, sheetName] of Object.entries(sheetNames)) {
      let sheet = spreadsheet.getSheetByName(sheetName);
      
      if (!sheet) {
        // Create the sheet if it doesn't exist
        sheet = spreadsheet.insertSheet(sheetName);
        
        // Add headers to new sheet (except for ERROR_LOG which has its own headers)
        if (key !== 'ERROR_LOG') {
          sheet.appendRow(settings.COLUMN_HEADERS);
          sheet.getRange(1, 1, 1, settings.COLUMN_HEADERS.length).setFontWeight("bold");
        } else {
          sheet.appendRow(settings.ERROR_LOG_HEADERS);
          sheet.getRange(1, 1, 1, settings.ERROR_LOG_HEADERS.length).setFontWeight("bold");
        }
      }
      
      sheets[key] = sheet;
    }
    
    return sheets;
  } catch (error) {
    logError("createAllSheets", error, { spreadsheetId: spreadsheet.getId() });
    throw error; // Re-throw as this is a critical function
  }
}

/**
 * Moves a patient from one sheet to another
 * 
 * @param {Object} sourceSheet - Sheet to move from
 * @param {Object} targetSheet - Sheet to move to
 * @param {Array} rowData - The patient data to move
 * @param {number} sourceRow - Row number in the source sheet
 * @param {string} patientName - Name of the patient (for logging)
 * @param {string} status - Status that triggered the move
 * @returns {boolean} Whether the move was successful
 */
function movePatient(sourceSheet, targetSheet, rowData, sourceRow, patientName, status) {
  try {
    if (!sourceSheet || !targetSheet) {
      logError("movePatient", "Source or target sheet is missing", { 
        sourceSheet: sourceSheet ? sourceSheet.getName() : null,
        targetSheet: targetSheet ? targetSheet.getName() : null
      });
      return false;
    }
    
    // Add to target sheet
    targetSheet.appendRow(rowData);
    
    // Remove from source sheet
    sourceSheet.deleteRow(sourceRow);
    
    // Log the action
    console.log(
      `Moved patient ${patientName} (row ${sourceRow}) with status "${status}" to ${targetSheet.getName()} sheet.`
    );
    
    return true;
  } catch (error) {
    logError("movePatient", error, { 
      sourceSheet: sourceSheet ? sourceSheet.getName() : null,
      targetSheet: targetSheet ? targetSheet.getName() : null,
      sourceRow,
      patientName,
      status
    });
    return false;
  }
}

/**
 * Gets the header row from a sheet
 * 
 * @param {Object} sheet - The sheet to get headers from
 * @param {Array} defaultHeaders - Default headers to use if none found
 * @returns {Array} The header row
 */
function getHeaderRow(sheet, defaultHeaders) {
  try {
    if (!sheet || sheet.getLastRow() === 0) {
      return defaultHeaders;
    }
    
    const lastColumn = Math.max(sheet.getLastColumn(), defaultHeaders.length);
    return sheet.getRange(1, 1, 1, lastColumn).getValues()[0];
  } catch (error) {
    logError("getHeaderRow", error, { sheetName: sheet ? sheet.getName() : null });
    return defaultHeaders;
  }
}
