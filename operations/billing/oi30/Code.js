function onEdit(e) {
  // Verify that we have a valid edit event
  if (!e || !e.range || !e.source) {
    Logger.log('Invalid edit event');
    return;
  }
  var sheet = e.range.getSheet();
  var range = e.range;
  try {
    // Check if the edit was made in either of the source sheets
    var sheetName = sheet.getName();
    if (sheetName !== "OI over 30" && sheetName !== "OI15") {
      return;
    }
    // Check if the edit was made in column H
    if (range.getColumn() !== 8) {
      return;
    }
    // Check if the edit was made in row 8 or below
    if (range.getRow() < 8) {
      return;
    }
    // Check if the selected value is "Needs Office Attention - Doctor"
    var editedValue = range.getValue();
    if (editedValue !== "Needs Office Attention - Doctor") {
      return;
    }
    // Get the active spreadsheet and the source row data
    var ss = e.source;
    var sourceRow = sheet.getRange(range.getRow(), 1, 1, sheet.getLastColumn()).getValues()[0];
    // Get the destination sheet
    var destinationSheet = ss.getSheetByName("Discussion");
    if (!destinationSheet) {
      Logger.log("Destination sheet 'Discussion' not found");
      return;
    }
    // Find the first empty row in the destination sheet
    var lastRow = getFirstEmptyRow(destinationSheet);
    // Copy the data to the destination sheet
    destinationSheet.getRange(lastRow, 1, 1, sourceRow.length).setValues([sourceRow]);
    // Log success
    Logger.log(`Successfully copied row ${range.getRow()} from sheet "${sheetName}" to "Discussion" sheet at row ${lastRow}`);
  } catch (error) {
    Logger.log(`Error in onEdit: ${error.message}\n${error.stack}`);
  }
}

// Helper function to find the first empty row
function getFirstEmptyRow(sheet) {
  var lastRow = sheet.getLastRow();
  if (lastRow === 0) {
    return 1;
  }
  // Check if the last row is empty
  var lastRowValue = sheet.getRange(lastRow, 1).getValue();
  if (lastRowValue === "") {
    // If last row is empty, search upward for the first non-empty row
    for (var i = lastRow; i >= 1; i--) {
      if (sheet.getRange(i, 1).getValue() !== "") {
        return i + 1;
      }
    }
    return 1;
  }
  // If last row is not empty, return next row
  return lastRow + 1;
}