{
  "compilerOptions": {
    /* Basic Options */
    "target": "ES2019",                   /* Specify ECMAScript target version: Apps Script V8 runtime supports modern syntax. */
    "lib": ["ESNext", "ScriptHost"],      /* Specify library files to be included in the compilation. 'ScriptHost' is for GAS environment. */
    "allowJs": true,                 /* Allow javascript files to be compiled. */
    "checkJs": true,                 /* Report errors in .js files. */
    "noEmit": true,                  /* Do not emit outputs. clasp handles the transpilation/bundling. */
    "isolatedModules": true,         /* Ensure that each file can be safely transpiled without relying on other imports -- useful for clasp. */

    /* Strict Type-Checking Options */
    "strict": true,                  /* Enable all strict type-checking options. */

    /* Module Resolution Options */
    "moduleResolution": "node",    /* Specify module resolution strategy: 'node' for typical Node.js/npm setup. */
    "esModuleInterop": true,         /* Enables emit interoperability between CommonJS and ES Modules via cre ation of namespace objects for all imports. */
    "types": ["google-apps-script", "jest"], /* Type declaration files to be included in compilation. */

    /* Advanced Options */
    "skipLibCheck": true,            /* Skip type checking of declaration files. */
    "forceConsistentCasingInFileNames": true /* Disallow inconsistently-cased references to the same file. */
  },
  "include": [
    "**/*.gs",   // or "**/*.js" if you only use .js
    "**/*.js"
  ],
  "exclude": [
    "node_modules"
  ]
} 